import asyncio
from typing import List, Optional, Any, Coroutine

from fastapi import APIRouter
from pydantic import BaseModel
from loguru import logger
from t_ai_agent.ai_proxy_client_factory import AiProxyClientFactory
from t_ai_agent.utils.attachment_handler import create_client, create_file, fetch_attachment
from t_ai_app import G
from t_ai_mcp import TMCPHelper

ai_proxy_client_factory = AiProxyClientFactory()

attachment_router = APIRouter(prefix="/attachment")


class AttachmentInfo(BaseModel):
    url: str
    name: str


class AttachmentAnalyzeRequest(BaseModel):
    attachments: List[AttachmentInfo]
    prompt: str = None


# 路由处理函数
@attachment_router.post("/analyze")
async def analyze_attachment(req: AttachmentAnalyzeRequest):
    """
    分析附件 URL 列表，返回 {文件名: 分析结果文本} 的 map
    """
    client = await create_client("2449b943-e9a4-4fc2-98a0-45eaa334eb04")

    tasks = [process_url(attachment.url, client, req.prompt, attachment.name) for attachment in req.attachments]
    results = await asyncio.gather(*tasks)
    
    result_map = {}
    for result in results:
        if result is not None:
            filename, result_text = result
            result_map[filename] = result_text
    return result_map


async def process_url(url: str, client, prompt: str, filename: str) -> tuple[str, str] | None:
    """处理单个URL的附件分析"""
    try:
        file_data = await fetch_attachment(url)
        if not file_data:
            return None

        file_extension = filename.split(".")[-1].lower()

        # 图片类型和PDF使用MCP服务处理
        if file_extension in ["jpg", "jpeg", "png", "gif", "bmp", "webp", "pdf"]:
            result_text = await process_with_mcp(url, filename)
            if result_text and result_text.strip():
                return filename, result_text
            return None
        # 文本类型使用大模型分析
        elif file_extension in ["txt", "csv", "md"]:
            content = file_data.decode("utf-8")
            result_text = await analyze_with_ai(client, content, prompt)
            return filename, result_text
        else:
            # 其他类型尝试使用文件提取工具
            file_object = await create_file(client, "file-extract", file_data)
            if not file_object:
                return filename, "文件上传失败，无法获取文件对象"

            content = f"fileid://{file_object.id}"
            result_text = await analyze_with_ai(client, content, prompt)
            return filename, result_text
    except Exception as e:
        logger.error(f"处理附件失败 {filename}: {e}")
        return filename, f"分析失败: {str(e)}"


async def process_with_mcp(url: str,  filename: str) -> str:
    """使用MCP服务处理图片和PDF文件"""
    try:
        # 图片和PDF都使用相同的MCP服务配置
        mcp_request = {
            "server_name": "mcp-ocr",
            "server_version": "1.0.0",
            "tool_name": "ocr",
            "tool_args": {
                "image_url": url
            }
        }
        from t_ai_web.routers.mcp import run_mcp_tool
        
        try:
            result = await run_mcp_tool(mcp_request)
        
            if result.get("success"):
                # MCP服务成功，直接返回data，不管是否为空
                data = result.get("data")
                return str(data) if data is not None else ""
            else:
                return None
        except Exception as e:
            logger.error(f"MCP服务调用失败 {mcp_request['server_name']}: {e}")
            return f"MCP服务调用失败: {str(e)}"

    except Exception as e:
        logger.error(f"MCP处理失败 {filename}: {e}")
        return f"MCP处理失败: {str(e)}"


async def analyze_with_ai(client, content: str, prompt: str) -> str:
    """使用AI分析内容"""
    completion = client.chat.completions.create(
        model="qwen-long",
        messages=[{"role": "system", "content": content}, {"role": "user", "content": prompt or "分析下文件里的内容"}],
        stream=True,
    )
    return "".join(
        chunk.choices[0].delta.content for chunk in completion if chunk.choices and chunk.choices[0].delta.content
    )
