[project]
name = "t-ai-web"
version = "0.1.0"
description = "t-ai-web包 主要是 web相关应用"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    # internal
    "t-ai-agent",
    "t-ai-agent-ops",
    "t-ai-app",
    "t-ai-chat",
    "t-ai-knowledge-base",
    "t-ai-docx",
    "t-ai-mcp",
    "t-ai-common",
    "t-ai-deepresearch",

    # external
    "fastapi[standard]>=0.115.11",
    "starlette>=0.46",
    "uvicorn[standard]>=0.34.0",
    "loguru>=0.7.3",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.8.1",
    "debugpy>=1.8.13",
    "langfuse>=2.50.0,<3.0.0",
    "arize-phoenix-otel>=0.12.1",
    "httpx>=0.28.1",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/t_ai_web"]
