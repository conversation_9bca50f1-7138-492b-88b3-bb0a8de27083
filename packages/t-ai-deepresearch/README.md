# DeerFlow - Deep Research AI Agent

基于 LangGraph 的智能深度研究系统，支持流式执行和多种工具集成。

## 特性

- 基于 LangGraph 的工作流程
- 支持多种搜索引擎（ArXiv、DuckDuckGo）
- MCP 工具集成
- 流式响应
- 深度思考模式

## 安装

```bash
uv sync
```

## 使用

```bash
# 根目录启动 LangGraph Studio 调试
export LANGGRAPH_STUDIO_MODE=true && uvx --refresh --from "langgraph-cli[inmem]" --with-editable . --python 3.12 langgraph dev --config packages/t-ai-deepresearch/src/langgraph.json --allow-blocking --server-log-level info
```

## 配置

详见 `langgraph.json` 配置文件。
