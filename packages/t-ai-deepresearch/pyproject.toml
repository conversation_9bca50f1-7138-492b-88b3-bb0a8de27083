[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "t-ai-deepresearch"
version = "0.1.0"
description = "t-ai-deepresearch"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "langchain-community>=0.3.19",
    "langchain-experimental>=0.3.4",
    "langchain-openai>=0.3.8",
    "langgraph>=0.3.5",
    "readabilipy>=0.3.0",
    "python-dotenv>=1.0.1",

    "markdownify>=1.1.0",
    "fastapi>=0.110.0",
    "json-repair>=0.7.0",
    "jinja2>=3.1.3",
    "langchain-mcp-adapters>=0.0.9",
    "langchain-deepseek>=0.1.3",

    "t-ai-agent",
    "t-ai-app",

    "langchain-core>=0.3.0",
    "loguru>=0.7.0",
    "openai>=1.0.0",
    "pydantic>=2.0.0",
    "requests>=2.25.0",
    "pyyaml>=6.0.0",
]

[project.optional-dependencies]
dev = [
    "ruff",
    "black>=24.2.0",
    "langgraph-cli[inmem]>=0.4.0",
]
test = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=1.0.0",
]

[tool.uv]
required-version = ">=0.6.15"

# 添加workspace sources配置以解析内部依赖
[tool.uv.sources]
t-ai-agent = { workspace = true }

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=src --cov-report=term-missing"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::UserWarning",
]

[tool.coverage.report]
fail_under = 25

[tool.hatch.build.targets.wheel]
packages = ["src/t_ai_deepresearch"]

[tool.black]
line-length = 88
target-version = ["py312"]
include = '\.pyi?$'
extend-exclude = '''
# A regex preceded with ^/ will apply only to files and directories
# in the root of the project.
^/build/
'''
