"""
Studio 调试用的 agent 配置示例
"""

import json
from pathlib import Path


def create_studio_agent_config():
    """创建用于 Studio 调试的 agent 配置"""
    # 从 JSON 文件读取配置
    config_path = Path(__file__).parent / "studio_config.json"
    
    if not config_path.exists():
        raise FileNotFoundError(f"Studio 配置文件不存在: {config_path}")
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
            return StudioAgentConfig(config_data)
    except Exception as e:
        raise RuntimeError(f"读取 Studio 配置文件失败: {e}")


class StudioAgentConfig:
    """Studio 调试用的 agent 配置"""
    
    def __init__(self, data):
        self._load_from_data(data)
    
    def _load_from_data(self, data):
        """从 JSON 数据加载配置"""
        agent_data = data.get("agent", {})
        self.name = agent_data.get("name", "Studio 调试 Agent")
        self.key = agent_data.get("key", "studio_debug_agent")
        self.props = StudioAgentProperties(agent_data.get("props", {}))


class StudioAgentProperties:
    """简化的 agent 属性"""
    
    def __init__(self, data):
        self._load_from_data(data)
    
    def _load_from_data(self, data):
        """从数据加载属性"""
        self.model = StudioLlmModel(data.get("model", {}))
        self.deepResearch = StudioDeepResearchConfig(data.get("deepResearch", {}))
        self.skill_tools = data.get("skillTools", [])
        self.systemPrompt = data.get("systemPrompt", "你是一个智能研究助手")
        self.greetings = data.get("greetings", "你好！我是你的研究助手")


class StudioLlmModel:
    """简化的 LLM 模型配置"""
    
    def __init__(self, data):
        self._load_from_data(data)
    
    def _load_from_data(self, data):
        """从数据加载模型配置"""
        self.model_publisher = data.get("modelPublisher", "bytedance")
        self.name = data.get("name", "doubao-1.5-thinking-pro-250415")
        self.setting = StudioLlmModelSetting(data.get("setting", {}))


class StudioLlmModelSetting:
    """简化的 LLM 模型设置"""
    
    def __init__(self, data):
        self._load_from_data(data)
    
    def _load_from_data(self, data):
        """从数据加载设置"""
        self.temperature = data.get("temperature", 0.0)
        self.top_p = data.get("topP", 1)
        self.reasoning_type = data.get("reasoningType", "REASONING")
        self.max_tokens = data.get("maxTokens", 4000)
        self.mode = data.get("mode", "Precision")


class StudioDeepResearchConfig:
    """简化的深度研究配置"""
    
    def __init__(self, data):
        self._load_from_data(data)
    
    def _load_from_data(self, data):
        """从数据加载配置"""
        self.enabled = data.get("enabled", True)
        self.max_plan_iterations = data.get("maxPlanIterations", 1)
        self.max_step_num = data.get("maxStepNum", 3)
        self.max_search_results = data.get("maxSearchResults", 5)
        self.auto_accepted_plan = data.get("autoAcceptedPlan", False)
        self.enable_background_investigation = data.get("enableBackgroundInvestigation", True)
        self.show_research_button = data.get("showResearchButton", True)
        self.engine_config = data.get("engineConfig", {})


# 导出配置
STUDIO_AGENT_CONFIG = create_studio_agent_config()
