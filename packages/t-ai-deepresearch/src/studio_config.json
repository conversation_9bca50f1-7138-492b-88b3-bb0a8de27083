{"agent": {"type": "Agent", "key": "QA$mdAgent0703", "name": "测试模型", "props": {"type": "AgentProperties", "model": {"modelPublisher": "bytedance", "name": "doubao-1.5-thinking-pro-250415", "setting": {"mode": "Precision", "temperature": 0, "topP": 1}}, "systemPrompt": "你是一个助手", "greetings": "你好，我是模型创建agent，请问我有什么可以帮助你的吗？", "userQuestionsSuggest": false, "userQuestionsCustom": false, "skillTools": [{"type": "mcp", "key": "github-trending", "sourceType": "third-party", "transport": "stdio", "visible": true, "tools": [{"id": "get_github_trending_repositories", "key": "get_github_trending_repositories", "name": "get_github_trending_repositories", "desc": "Use this tool when you need to find the most popular and trending repositories on GitHub.", "input": []}, {"id": "get_github_trending_developers", "key": "get_github_trending_developers", "name": "get_github_trending_developers", "desc": "Get trending developers on GitHub.", "input": []}], "name": "GitHub Trending", "mcpServerVersion": "1.0.0", "mcpServerEndpoint": "uvx mcp-github-trending", "desc": "GitHub trending data provider, offers trending repositories and developers information"}, {"key": "search-engine", "name": "search-engine", "desc": "search-engine 是一个为LLM提供联网搜索的工具", "type": "mcp", "visible": true, "finalOutput": false, "mcpServerEndpoint": "https://mcp-proxy.erda.cloud/proxy/connect/search-engine/1.0.0", "mcpServerVersion": "1.0.0", "sourceType": "ai-proxy", "provider": "erda", "tools": [{"id": "be0af385-2b09-4f8a-850c-c562aa881240-search", "key": "search", "name": "search", "desc": "search information from internet", "input": [{"fieldKey": "fetch_all", "fieldAlias": "fetch_all", "fieldName": "fetch_all", "fieldType": "Boolean", "description": "fetch all the information in this url, if not set, the default fetch 20000 characters", "required": false}, {"fieldKey": "need_fetch", "fieldAlias": "need_fetch", "fieldName": "need_fetch", "fieldType": "Boolean", "description": "need fetch the urls to summarize the information detail", "required": false}, {"fieldKey": "query", "fieldAlias": "query", "fieldName": "query", "fieldType": "Text", "description": "query information from internet", "required": true}]}]}, {"type": "service", "key": "AI$SYS_PagingDataService", "name": "(系统)查询分页数据服务", "desc": "可以查询任何模型业务数据，请根据模型key查询", "finalOutput": false, "input": [{"fieldKey": "<PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Text", "description": "数据模型的 key，带 $ 符的", "required": true}, {"fieldKey": "request", "fieldName": "request", "fieldType": "Object", "elements": [{"fieldKey": "pageable", "fieldName": "pageable", "fieldType": "Pageable", "elements": [{"fieldKey": "pageNo", "fieldName": "pageNo", "fieldType": "Number"}, {"fieldKey": "pageSize", "fieldName": "pageSize", "fieldType": "Number"}, {"fieldKey": "conditionGroup", "fieldName": "conditionGroup", "fieldType": "ConditionGroup"}, {"fieldKey": "conditionItems", "fieldName": "conditionItems", "fieldType": "ConditionItems", "description": "筛选条件"}]}, {"fieldKey": "sortOrders", "fieldName": "sortOrders", "fieldType": "Array", "element": {"fieldKey": "element", "fieldName": "element", "fieldType": "Object", "elements": [{"fieldKey": "<PERSON><PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON><PERSON><PERSON>", "fieldType": "Text"}, {"fieldKey": "sortType", "fieldName": "sortType", "fieldType": "Text"}]}}]}]}], "input": [{"fieldKey": "userContent", "fieldAlias": "userContent", "fieldName": "userContent", "fieldType": "Text"}, {"fieldKey": "sessionId", "fieldAlias": "sessionId", "fieldName": "sessionId", "fieldType": "Text"}, {"fieldKey": "attachments", "fieldAlias": "attachments", "fieldName": "attachments", "fieldType": "Array", "element": {"fieldKey": "element", "fieldAlias": "element", "fieldName": "element", "fieldType": "Attachment"}}], "relatedModels": [{"modelKey": "GEN_MD$gen_business_partner_md", "modelAlias": "GEN_MD$gen_business_partner_md", "modelName": "合作伙伴"}, {"modelKey": "GEN_MD$gen_business_partner_type_cf", "modelAlias": "GEN_MD$gen_business_partner_type_cf", "modelName": "合作伙伴类型"}, {"modelKey": "GEN_MD$gen_coun_type_cf", "modelAlias": "GEN_MD$gen_coun_type_cf", "modelName": "国家配置"}, {"modelKey": "GEN_MD$gen_bank_cf", "modelAlias": "GEN_MD$gen_bank_cf", "modelName": "银行配置"}, {"modelKey": "GEN_MD$gen_sub_bank_cf", "modelAlias": "GEN_MD$gen_sub_bank_cf", "modelName": "银行支行"}, {"modelKey": "GEN_MD$gen_addr_type_cf", "modelAlias": "GEN_MD$gen_addr_type_cf", "modelName": "地址库"}, {"modelKey": "GEN_MD$gen_mat_cate_md", "modelAlias": "GEN_MD$gen_mat_cate_md", "modelName": "类目配置"}, {"modelKey": "GEN_MD$gen_mat_type_cf", "modelAlias": "GEN_MD$gen_mat_type_cf", "modelName": "物料类型"}, {"modelKey": "GEN_MD$gen_uom_type_cf", "modelAlias": "GEN_MD$gen_uom_type_cf", "modelName": "计量单位"}], "longTermMemoryConfig": {"enabled": true}, "deepResearch": {"enabled": true, "maxPlanIterations": 1, "maxStepNum": 1, "maxSearchResults": 1, "autoAcceptedPlan": false, "enable_background_investigation": false}}}}