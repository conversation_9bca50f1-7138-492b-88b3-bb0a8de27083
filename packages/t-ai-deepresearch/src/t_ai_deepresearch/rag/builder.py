# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from t_ai_deepresearch.config.tools import SELECTED_RAG_PROVIDER, RAGProvider
from t_ai_deepresearch.rag.ragflow import RAGFlowProvider
from t_ai_deepresearch.rag.vikingdb_knowledge_base import VikingDBKnowledgeBaseProvider
from t_ai_deepresearch.rag.retriever import Retriever


def build_retriever() -> Retriever | None:
    if SELECTED_RAG_PROVIDER == RAGProvider.RAGFLOW.value:
        return RAGFlowProvider()
    elif SELECTED_RAG_PROVIDER == RAGProvider.VIKINGDB_KNOWLEDGE_BASE.value:
        return VikingDBKnowledgeBaseProvider()
    elif SELECTED_RAG_PROVIDER:
        raise ValueError(f"Unsupported RAG provider: {SELECTED_RAG_PROVIDER}")
    return None
