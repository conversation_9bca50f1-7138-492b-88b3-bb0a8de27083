# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from typing import List, Optional

from pydantic import BaseModel, Field

from t_ai_agent.model.agent_dsl import AgentMeta


class ChatMessage(BaseModel):
    """聊天消息模型"""
    role: str = Field(..., description="消息角色")
    content: str = Field(..., description="消息内容")


class DeepResearchRequest(BaseModel):
    """深度研究请求模型"""
    session_id: str = Field(..., alias="sessionId", description="会话ID，对应 thread_id")
    agent: AgentMeta = Field(..., description="Agent 配置")
    messages: List[ChatMessage] = Field(..., description="对话消息列表")
    interrupt_feedback: Optional[str] = Field(None, alias="interruptFeedback", description="中断反馈，用于恢复中断的流程")


class GeneratePPTRequest(BaseModel):
    content: str = Field(..., description="The content of the ppt")

