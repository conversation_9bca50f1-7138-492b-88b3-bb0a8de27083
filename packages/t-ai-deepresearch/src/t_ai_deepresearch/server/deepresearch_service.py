# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
import os
from typing import List, Dict, Any, cast
from fastapi import HTTPException
from fastapi.responses import StreamingResponse
from langchain_core.messages import AIMessageChunk, BaseMessage, ToolMessage
from langgraph.types import Command

from t_ai_agent.model.agent_dsl import Agent<PERSON><PERSON>
from t_ai_deepresearch.llms.llm import create_llm_from_agent_model
from t_ai_deepresearch.config.configuration import get_recursion_limit, Configuration
from t_ai_deepresearch.config.report_style import ReportStyle
from t_ai_deepresearch.graph.builder import build_graph_with_memory
from t_ai_deepresearch.config.agent_config_builder import AgentConfigBuilder
from t_ai_deepresearch.server.chat_request import (
    DeepResearchRequest
)

logger = logging.getLogger(__name__)

graph = build_graph_with_memory()

async def deepresearch_run_streamed(request: DeepResearchRequest):
    """深度研究流式执行函数"""
    # 检查深度研究功能是否启用
    if not request.agent.props.deepResearch.enabled:
        raise HTTPException(
            status_code=403,
            detail="深度研究功能未启用。请在 agent 配置中设置 deepResearch.enabled = true"
        )
    
    # 直接提取模型对象，避免重复序列化
    model_obj = AgentConfigBuilder.extract_model_object_from_agent_dsl(request.agent)
    
    # 使用 sessionId 作为 thread_id
    thread_id = request.session_id if request.session_id else "__default__"
    
    # 创建配置对象，自动解析 agent 配置
    config = Configuration(
        agent=request.agent,
        thread_id=thread_id,
        model_obj=model_obj
    )
    
    return StreamingResponse(
        _astream_workflow_generator(
            [msg.model_dump() for msg in request.messages],
            config,  # 传递整个配置对象
            request.interrupt_feedback or "",  # 使用请求中的 interrupt_feedback
            request.agent,
        ),
        media_type="text/event-stream",
    )


async def _astream_workflow_generator(
    messages: List[dict],
    config: Configuration,  # 直接使用配置对象
    interrupt_feedback: str,
    agent: AgentMeta
):
    # 直接访问配置属性，类型安全
    input_ = {
        "messages": messages,
        "plan_iterations": 0,
        "final_report": "",
        "current_plan": None,
        "observations": [],
        "auto_accepted_plan": config.auto_accepted_plan,  # 类型安全
        "enable_background_investigation": config.enable_background_investigation,
        "research_topic": messages[-1]["content"] if messages else "",
    }
    if not config.auto_accepted_plan and interrupt_feedback:
        resume_msg = f"[{interrupt_feedback}]"
        # add the last message to the resume message
        if messages:
            resume_msg += f" {messages[-1]['content']}"
        input_ = Command(resume=resume_msg)
    
    # 预创建并缓存当前线程的 LLM，避免在可序列化配置中传递实例
    try:
        llm_instance = await create_llm_from_agent_model(config.model_obj)
        Configuration.register_llm(config.thread_id, llm_instance)
    except Exception as e:
        logger.warning(f"预创建 LLM 失败，将回退到默认 LLM: {e}")

    # 构建运行时配置，使用配置对象的属性
    runtime_config = {
        "configurable": {
            "thread_id": config.thread_id,
            "resources": config.resources,
            "recursion_limit": get_recursion_limit(),
            # 使用配置对象的属性，类型安全
            "max_plan_iterations": config.max_plan_iterations,
            "max_step_num": config.max_step_num,
            "max_search_results": config.max_search_results,
            "auto_accepted_plan": config.auto_accepted_plan,
            "enable_background_investigation": config.enable_background_investigation,
            "enable_deep_thinking": config.enable_deep_thinking,
            "mcp_settings": config.mcp_settings,
            "service_settings": config.service_settings,
            "report_style": config.report_style,
            # 传递模型对象，节点中按需创建与缓存 LLM，避免序列化问题
            "model_obj": config.model_obj,
            "agent_meta": agent,
        }
    }
    
    async for agent, _, event_data in graph.astream(
        input_,
        config=runtime_config,
        stream_mode=["messages", "updates"],
        subgraphs=True,
    ):
        if isinstance(event_data, dict):
            if "__interrupt__" in event_data:
                yield _make_event(
                    "interrupt",
                    {
                        "role": "assistant",
                        "content": event_data["__interrupt__"][0].value,
                        "finish_reason": "interrupt",
                        "options": [
                            {"text": "Edit plan", "value": "edit_plan"},
                            {"text": "Start research", "value": "accepted"},
                        ],
                    },
                )
            continue
        message_chunk, message_metadata = cast(
            tuple[BaseMessage, dict[str, any]], event_data
        )
        # Handle empty agent tuple gracefully
        agent_name = "planner"
        if agent and len(agent) > 0:
            agent_name = agent[0].split(":")[0] if ":" in agent[0] else agent[0]
        event_stream_message: dict[str, any] = {
            "agent": agent_name,
            "content": message_chunk.content,
        }
        if message_chunk.additional_kwargs.get("reasoning_content"):
            event_stream_message["reasoning_content"] = message_chunk.additional_kwargs[
                "reasoning_content"
            ]
        if message_chunk.response_metadata.get("finish_reason"):
            event_stream_message["finish_reason"] = message_chunk.response_metadata.get(
                "finish_reason"
            )
        if isinstance(message_chunk, ToolMessage):
            # Tool Message - Return the result of the tool call
            event_stream_message["tool_call_id"] = message_chunk.tool_call_id
            yield _make_event("tool_call_result", event_stream_message)
        elif isinstance(message_chunk, AIMessageChunk):
            # AI Message - Raw message tokens
            if message_chunk.tool_calls:
                # AI Message - Tool Call
                event_stream_message["tool_calls"] = message_chunk.tool_calls
                event_stream_message["tool_call_chunks"] = (
                    message_chunk.tool_call_chunks
                )
                yield _make_event("tool_calls", event_stream_message)
            elif message_chunk.tool_call_chunks:
                # AI Message - Tool Call Chunks
                event_stream_message["tool_call_chunks"] = (
                    message_chunk.tool_call_chunks
                )
                yield _make_event("tool_call_chunks", event_stream_message)
            else:
                # AI Message - Raw message tokens
                yield _make_event("message_chunk", event_stream_message)


def _make_event(event_type: str, data: dict[str, any]):
    if data.get("content") == "":
        data.pop("content")
    return f"event: {event_type}\ndata: {json.dumps(data, ensure_ascii=False)}\n\n"