# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from typing import Dict, Any, Optional
from langchain_openai import ChatOpenAI
from t_ai_deepresearch.config.configuration import _LLM_CACHE
from langchain_deepseek import <PERSON>tDeepSeek
from langchain_core.language_models.chat_models import BaseChatModel
from t_ai_agent.ai_proxy_client_factory import ai_proxy_client_factory
from t_ai_agent.model.agent_dsl import LlmModel
from loguru import logger

async def create_llm_from_agent_model(agent_model:LlmModel):
    """
    从 agent 的模型配置创建 LLM
    
    Args:
        agent_model: agent_meta.props.model 对象
        ai_proxy_client_factory: AI Proxy 客户端工厂
        
    Returns:
        配置好的 LLM 实例（langchain ChatOpenAI）
    """
    model_publisher = agent_model.model_publisher
    model_name = agent_model.name
    model_settings = agent_model.setting
    
    # 创建 OpenAI 客户端
    openai_client = await ai_proxy_client_factory.get_client(model_publisher)
    
    # 处理 headers，过滤掉 openai.Omit 对象
    default_headers = {}
    if openai_client.default_headers:
        for key, value in openai_client.default_headers.items():
            # 只保留字符串类型的值，过滤掉 openai.Omit 对象
            if isinstance(value, str) and value:
                default_headers[key] = value
    
    thinking_type = model_settings.get_thinking_type()
    
    # 准备基础模型配置参数
    model_kwargs = {
        "model": model_name,
        "temperature": model_settings.temperature,
        "top_p": model_settings.top_p,
        "max_tokens": model_settings.max_tokens,
        "api_key": openai_client.api_key,
        "max_retries": 3,
    }
  
    # 只有在有 headers 时才添加
    if default_headers:
        model_kwargs["default_headers"] = default_headers
    
    if thinking_type == "enabled":
        model_kwargs["api_base"] = str(openai_client.base_url)
        return ChatDeepSeek(**model_kwargs)
    else:
        # ChatOpenAI 使用 base_url
        model_kwargs["base_url"] = str(openai_client.base_url)
        return ChatOpenAI(**model_kwargs)
