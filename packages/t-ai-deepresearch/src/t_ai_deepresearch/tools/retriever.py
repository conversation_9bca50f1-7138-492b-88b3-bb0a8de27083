# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from langchain_core.tools import tool
from typing import List


def get_retriever_tool(resources: List[dict]):
    """
    根据资源列表创建检索工具
    
    Args:
        resources: 资源文件列表
        
    Returns:
        检索工具实例，如果没有资源则返回 None
    """
    if not resources:
        return None
    
    @tool
    def local_search_tool(query: str) -> str:
        """
        在本地资源文件中搜索相关信息
        
        Args:
            query: 搜索查询
            
        Returns:
            搜索结果
        """
        # 简单实现：暂时返回提示信息
        return f"本地搜索功能暂未实现，查询：{query}"
    
    return local_search_tool
