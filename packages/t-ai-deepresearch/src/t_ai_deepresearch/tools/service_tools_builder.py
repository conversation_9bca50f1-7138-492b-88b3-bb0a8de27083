# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
Service 工具构建器

负责将 t-ai-agent 的 service 工具适配为 LangChain 工具，供 deepresearch 使用。
直接参考 create_service_tool 的逻辑构建 StructuredTool，确保行为一致。
"""


from loguru import logger
from langchain_core.tools import StructuredTool
from t_ai_agent.agent_tool import create_service_structured_tool
       
# 导入 t-ai-agent 相关模块
from t_ai_agent.model.agent_dsl import AgentMeta, ServiceTool, FieldMeta
from t_ai_deepresearch.graph.types import State

class ServiceToolsBuilder:
    """Service 工具构建器，直接参考 create_service_tool 逻辑构建 LangChain 工具"""
    
    def __init__(self, main_agent_name: str):
        """
        初始化构建器
        
        Args:
            main_agent_name: 主 agent 名称，用于创建 AgentMeta
        """
        self.main_agent_name = main_agent_name
        # 默认的请求头信息
        self.default_headers = {
            "T-AI-CALLBACK": "https://t-erp-portal-dev.app.terminus.io",
            "T-AI-SOURCE-COOKIE": "taid=a6244ba6-eb22-4962-abf7-8f08e07464b6; _ga=GA1.1.17769368.1747816287; _ga_GGY77GT80X=GS2.1.s1747903412$o2$g0$t1747903412$j0$l0$h0; Hm_lvt_5c04d88621b64442372da67ce63efe4e=1753669512; _clck=cc0fw%7C2%7Cfy9%7C0%7C2045; trantor_v2_lng=zh-CN; t_iam_test=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbklkIjoiMjAyNTA4MjkxMTQzMjU4NjlkOGRhYWM4ZTA1ODM0ZDhhYWNkODlkOGNiNTAwNTM1YyIsImV4cGlyZSI6MjU5MjAwLCJwYXRoIjoiLyIsImRvbWFpbiI6InRlcm1pbnVzLmlvIiwiaHR0cE9ubHkiOnRydWUsInNlY3VyZSI6ZmFsc2UsImlzcyI6ImlhbSgzLjAuMjUwOS4wLVNOQVBTSE9UKSIsInN1YiI6ImlhbSB1c2VyIiwiZXhwIjoxNzU2ODc4MzQ2LCJuYmYiOjE3NTY2MTkxNDYsImlhdCI6MTc1NjYxOTE0NiwianRpIjoiOWMzMzExZWE2ZTA5NGMzMDliNTZlMGVjOTA1NjllMDEifQ.W6B8AAaBdDQq_1IFJmWzOO0y6PJazT3xCOQOFLmZ8oQ; emp_cookie=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbklkIjoiMjAyNTA5MDIxMDMyNTMyNTgwYTE1N2M3NGExZDI0OWEwYWQ0YjcwNGYzNTNhYWM3NiIsImV4cGlyZSI6MTIwOTYwMCwicGF0aCI6Ii8iLCJkb21haW4iOiJ0ZXJtaW51cy5pbyIsImh0dHBPbmx5Ijp0cnVlLCJzZWN1cmUiOnRydWUsImlzcyI6ImlhbSgyLjUuMjQuMTEzMC4wLVNOQVBTSE9UKSIsInN1YiI6ImlhbSB1c2VyIiwiZXhwIjoxNzU3OTkxMTg5LCJuYmYiOjE3NTY3ODE1ODksImlhdCI6MTc1Njc4MTU4OSwianRpIjoiZTBhMzMwZDU1OTFlNGYxYWI5MDhlY2RjMTliNDljNGIifQ.ixVRC5Why45_LrHRWTmfMz5tHz1Z3vICmi_G7Hq5jB0; terp_iam_dev=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0b2tlbklkIjoiMjAyNTA5MDIxMTIyMjQwMzczMjkzYjljYTk5MmI0ZmU4OWRiMTI0ZmI3NDRmNmI3NCIsImV4cGlyZSI6MjU5MjAwLCJwYXRoIjoiLyIsImRvbWFpbiI6InRlcm1pbnVzLmlvIiwiaHR0cE9ubHkiOnRydWUsInNlY3VyZSI6ZmFsc2UsImlzcyI6ImlhbSgyLjUuMjUuMDUzMC4wLVNOQVBTSE9UKSIsInN1YiI6ImlhbSB1c2VyIiwiZXhwIjoxNzU3MDQyNTUwLCJuYmYiOjE3NTY3ODMzNTAsImlhdCI6MTc1Njc4MzM1MCwianRpIjoiNmNmMWYyNmVmYzNlNGE1Y2E5NGYwNzcxZGVmOTY5ZTUifQ.qDvNbibYF_CJO_x143Vu06Syu5sO-8oeygoluVBYVzU",
            "T-AI-SOURCE-REFERER": "https://t-erp-portal-dev.app.terminus.io/TERP_PORTAL-TERPSAAS/TERP_PORTAL/TERP_PORTAL$ueK6ls/page?_tab_id=0tJmwR1gLT8A&sceneKey=GEN_MD%24GNE_MAT_VIEW&viewKey=GEN_MD%24GNE_MAT_VIEW%3Alist"
        }
    
    async def build_service_tools(self, config=None, state:State = None) -> list[StructuredTool]:
        """
        基于配置中的 service_settings 构建可供 deepresearch 使用的 LangChain 工具列表
        
        Args:
            config: Configuration 对象，包含 agent 信息和 service_settings
            
        Returns:
            list[StructuredTool]: LangChain 工具列表
        """
        # 确保 ReqCtx 可用
        self._ensure_req_ctx_available()
        
        service_tools: list[StructuredTool] = []
        
        if not config or not hasattr(config, 'service_settings') or not config.service_settings:
            logger.info("[service-tool] Service tools disabled or not configured")
            return service_tools
        
        service_settings = config.service_settings
        services = service_settings.get("services", {})
        if not services:
            logger.info("[service-tool] No services found in service_settings")
            return service_tools
        
        # 使用配置中的 agent 对象
        if config and hasattr(config, 'agent') and config.agent:
            agent_meta = config.agent
            logger.info(f"[service-tool] Using agent from config: {agent_meta.name}")
        else:
            logger.error("[service-tool] No agent found in config, cannot build service tools")
            return service_tools
        
        logger.info(f"[service-tool] Building {len(services)} service tools for agent: {agent_meta.name}")
        
        for service_key, service_config in services.items():
            try:
                # 构建 ServiceTool 对象
                service_tool = self._build_service_tool_config(service_key, service_config)
                
                # 直接构建 StructuredTool，参考 create_service_tool 的逻辑
                langchain_tool = await create_service_structured_tool(agent_meta, service_tool, state)
                
                service_tools.append(langchain_tool)
                logger.info(f"[service-tool] Successfully registered: name={service_tool.name}, sse={service_config.get('sse_mode', False)}, agent={agent_meta.name}")
                
            except Exception as e:
                logger.error(f"[service-tool] Failed to register service tool: key={service_key}, error={e}")
        
        logger.info(f"[service-tool] Final result: {len(service_tools)} service tools built")
        return service_tools
    
    def _ensure_req_ctx_available(self):
        """
        确保 ReqCtx 可用
        - Studio 模式：直接使用 default_headers
        - 非 Studio 模式：如果没有 ReqCtx 就报错
        """
        try:
            from t_ai_app.ctx.req_ctx import ReqCtx, ReqCtxHeader, ReqCtxModel
            import uuid
            import os
            
            # 检查是否是 Studio 模式
            is_studio_mode = os.getenv("LANGGRAPH_STUDIO_MODE", "false").lower() in ("1", "true", "yes")
            
            # 检查 ReqCtx 是否已经设置
            current_header = ReqCtx.get_header()
            if current_header and current_header.t_ai_callback:
                logger.debug("[service-tool] ReqCtx already available")
                return
                
            # 如果是 Studio 模式，直接使用 default_headers
            if is_studio_mode:
                logger.info("[service-tool] Studio mode detected, using default headers")
                header_model = ReqCtxHeader(**self.default_headers)
                req_ctx = ReqCtxModel(
                    header=header_model,
                    trace_id=str(uuid.uuid4())
                )
                ReqCtx._var.set(req_ctx)  # type: ignore[attr-defined]
                logger.info("[service-tool] ReqCtx initialized with default headers for Studio mode")
                return
            
            # 非 Studio 模式：如果没有 ReqCtx 就报错
            logger.error("[service-tool] Non-Studio mode: ReqCtx not available, service tools will fail")
            raise RuntimeError("ReqCtx not available. Service tools require proper request context.")
                
        except ImportError as e:
            logger.warning(f"[service-tool] Failed to import ReqCtx: {e}")
        except Exception as e:
            logger.error(f"[service-tool] Failed to initialize ReqCtx: {e}")
            raise
    

    
    def _build_service_tool_config(self, service_key: str, service_config: dict) -> ServiceTool:
        """构建 ServiceTool 配置对象"""
        # 将 input 字段转换为 FieldMeta 对象列表
        input_fields = []
        for field_config in service_config.get("input", []):
            if isinstance(field_config, dict):
                # 从字典创建 FieldMeta
                field_meta = FieldMeta(**field_config)
                input_fields.append(field_meta)
            elif hasattr(field_config, '__dict__'):
                # 如果已经是对象，直接使用
                input_fields.append(field_config)
        
        return ServiceTool(
            key=service_key,
            name=service_config.get("name", service_key),
            desc=service_config.get("desc", ""),
            sse_mode=service_config.get("sse_mode", False),
            visible=service_config.get("visible", True),
            final_output=service_config.get("final_output", False),
            input=input_fields if input_fields else None
        )