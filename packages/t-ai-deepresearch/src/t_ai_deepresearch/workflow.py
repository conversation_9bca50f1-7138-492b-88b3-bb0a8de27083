# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import logging
import os
import json
import uuid
from pathlib import Path
from typing import Optional, Dict, Any, List
from t_ai_deepresearch.graph import  build_graph
from langchain_core.language_models.chat_models import BaseChatModel
from t_ai_deepresearch.config.configuration import Configuration
from t_ai_deepresearch.config.agent_config_builder import AgentConfigBuilder
from t_ai_agent.model.agent_dsl import LlmModel, LlmModelSetting
from langchain_openai import ChatOpenAI
from langchain_deepseek import ChatDeepSeek
from openai import OpenAI


# Configure logging
logging.basicConfig(
    level=logging.INFO,  # Default level is INFO
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)


def enable_debug_logging():
    """Enable debug level logging for more detailed execution information."""
    logging.getLogger("src").setLevel(logging.DEBUG)


logger = logging.getLogger(__name__)


def get_sync_ai_proxy_client(model_publisher: str | None = None) -> OpenAI:
    """
    同步版本的 AI Proxy 客户端创建函数
    避免异步上下文问题
    """
    try:
        from t_ai_app import G
        from t_ai_app.ctx import ReqCtx
        
        headers = {}
        trace_id = ReqCtx.get_trace_id()
        if trace_id is not None:
            headers["X-Request-Id"] = trace_id
        
        if model_publisher is not None:
            headers["X-AI-Proxy-Model-Publisher"] = model_publisher
        
        client = OpenAI(
            api_key=G.APP_SETTING.ai_proxy.ai_proxy_api_key,
            base_url=G.APP_SETTING.ai_proxy.ai_proxy_base_url,
            timeout=G.APP_SETTING.ai_proxy.llm_timeout,
            default_headers=headers if headers else None
        )
        logger.info(f"创建 AI Proxy 客户端: base_url={client.base_url}, model_publisher={model_publisher}")
        return client
    except Exception as e:
        logger.error(f"无法创建 AI Proxy 客户端: {e}")
        # 重新抛出异常，让调用者处理
        raise


def create_simple_llm_from_config(model_config: Dict[str, Any]):
    """
    创建一个简单的 LLM 实例，避免复杂的异步依赖
    
    Args:
        model_config: 模型配置字典
        
    Returns:
        LLM 实例
    """
    # 从配置中提取参数，提供默认值
    model_publisher = model_config.get("modelPublisher", "bytedance")
    model_name = model_config.get("name", "gpt-3.5-turbo")
    setting = model_config.get("setting", {})
    temperature = setting.get("temperature", 0.0)
    top_p = setting.get("topP", 1.0)
    max_tokens = setting.get("maxTokens", 4096)
    reasoning_type = setting.get("reasoningType")
    
    # 获取同步客户端
    try:
        openai_client = get_sync_ai_proxy_client(model_publisher)
    except Exception as e:
        logger.error(f"无法获取 AI Proxy 客户端，创建基本的 LLM: {e}")
        # 如果无法获取客户端，创建一个基本的 ChatOpenAI 实例
        return ChatOpenAI(
            model=model_name,
            temperature=temperature,
            top_p=top_p,
            max_tokens=max_tokens,
            api_key="sk-dummy",  # 这将导致错误，但至少不会崩溃
        )
    
    # 处理 headers，过滤掉非字符串值
    default_headers = {}
    if openai_client.default_headers:
        for key, value in openai_client.default_headers.items():
            if isinstance(value, str) and value:
                default_headers[key] = value
    
    # 准备基础模型配置参数
    model_kwargs = {
        "model": model_name,
        "temperature": temperature,
        "top_p": top_p,
        "max_tokens": max_tokens,
        "api_key": openai_client.api_key,
        "max_retries": 3,
    }
    
    # 只有在有 headers 时才添加
    if default_headers:
        model_kwargs["default_headers"] = default_headers
    
    # 根据推理类型选择模型
    if reasoning_type == "REASONING":
        model_kwargs["api_base"] = str(openai_client.base_url)
        return ChatDeepSeek(**model_kwargs)
    else:
        # ChatOpenAI 使用 base_url
        model_kwargs["base_url"] = str(openai_client.base_url)
        return ChatOpenAI(**model_kwargs)


def create_llm_model_from_dict(model_config: Dict[str, Any]) -> LlmModel:
    """
    从字典配置创建 LlmModel 对象
    
    Args:
        model_config: 从配置文件加载的模型配置字典
        
    Returns:
        LlmModel 对象
    """
    # 创建 LlmModelSetting 对象
    setting_dict = model_config.get("setting", {})
    setting = LlmModelSetting(
        temperature=setting_dict.get("temperature", 0.0),
        top_p=setting_dict.get("topP", 1),
        max_tokens=setting_dict.get("maxTokens", 4096),
        reasoning_type=setting_dict.get("reasoningType")
    )
    
    # 创建 LlmModel 对象
    return LlmModel(
        model_publisher=model_config.get("modelPublisher", "bytedance"),
        name=model_config.get("name", "doubao-1.5-thinking-pro-250415"),
        setting=setting
    )


def load_studio_config() -> Optional[Dict[str, Any]]:
    """
    从 studio_config.json 文件加载配置。
    优先级：
    1. 环境变量 STUDIO_CONFIG_PATH 指定的路径
    2. 项目根目录的 studio_config.json
    3. None（使用默认配置）
    """
    # 尝试从环境变量获取配置文件路径
    config_path = os.getenv("STUDIO_CONFIG_PATH")
    if config_path and Path(config_path).exists():
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                logger.debug(f"从环境变量指定的路径加载 Studio 配置: {config_path}")
                return config_data
        except Exception as e:
            logger.warning(f"无法从环境变量指定的路径加载配置 {config_path}: {e}")
    
    # 尝试从项目根目录的 studio_config.json 加载
    studio_config_path = Path(__file__).parent.parent / "studio_config.json"
    if studio_config_path.exists():
        try:
            with open(studio_config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                logger.debug(f"从项目根目录加载 Studio 配置: {studio_config_path}")
                return config_data
        except Exception as e:
            logger.warning(f"无法从 studio_config.json 加载配置: {e}")
    
    logger.debug("未找到 Studio 配置文件，将使用默认配置")
    return None


def extract_config_defaults_from_json(studio_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    从 studio_config JSON 中提取节点配置的默认值。
    现在使用统一的 AgentConfigBuilder，返回包含 agent 对象的配置。
    
    Args:
        studio_config: 从 JSON 文件加载的完整配置
        
    Returns:
        包含 agent 对象的配置字典
    """
    return AgentConfigBuilder.build_config_from_json(studio_config)


# 全局缓存，避免重复加载配置文件
_json_config_cache = None


def get_json_config_defaults() -> Dict[str, Any]:
    """
    这个函数会被 Configuration.from_runnable_config 调用。
    现在返回包含 agent 对象的配置，Configuration 类会自动解析。
    """
    global _json_config_cache
    
    if _json_config_cache is None:
        studio_config = load_studio_config()
        if studio_config:
            # 先提取模型配置，避免在 extract_config_defaults_from_json 中修改原始 studio_config
            model_config = studio_config.get("agent", {}).get("props", {}).get("model", {})
            
            # 获取包含 agent 对象的配置
            agent_config = extract_config_defaults_from_json(studio_config)
            
            # 构建新的配置结构，包含 agent 对象和模型配置
            _json_config_cache = {
                **agent_config,  # 包含 agent 对象
                "model_config": model_config,  # 模型配置，用于按需创建 LLM
            }
            
            # 缓存 debug headers（仅在 Studio 模式调试用）
            debug_headers = studio_config.get("debugHeaders") or {}
            if debug_headers:
                _json_config_cache["debug_headers"] = debug_headers
                
            logger.info(f"[Studio] 已缓存配置，包含 agent 对象和 {len(_json_config_cache)} 个配置项")
        else:
            # 未提供 Studio 配置：返回空配置
            _json_config_cache = {}
    
    return _json_config_cache


def _is_studio_mode() -> bool:
    val = os.getenv("LANGGRAPH_STUDIO_MODE")
    if val is None:
        val = os.getenv("STUDIO_MODE", "false")
    return str(val).lower() in ("1", "true", "yes")




def prepare_studio_llm_for_thread() -> Optional[BaseChatModel]:
    """
    在 Studio 模式下，为指定 thread_id 按需创建并注册一个 LLM。

    Args:
        thread_id: 线程/会话 ID

    Returns:
        已创建并注册的 LLM；若非 Studio 模式或无配置则返回 None
    """
    try:
        studio_mode = os.getenv("LANGGRAPH_STUDIO_MODE", "false").lower() in ("1", "true", "yes")
        if not studio_mode:
            return None

        defaults = get_json_config_defaults()
        model_conf = defaults.get("model_config") if isinstance(defaults, dict) else None
        if not model_conf:
            logger.warning("[Studio] 未找到 model_config，跳过 LLM 注册")
            return None

        llm = create_simple_llm_from_config(model_conf)
        Configuration.register_llm("__studio__", llm)
        logger.info(f"[Studio] 已为 thread_id={"__studio__"} 注册 LLM")
        return llm
    except Exception as e:
        logger.warning(f"[Studio] 注册 LLM 失败: {e}")
        return None
    
prepare_studio_llm_for_thread()

# Create the default graph (for Studio debugging)
# 使用不带 checkpointer 的图，因为 LangGraph API 平台会自动处理持久化
graph = build_graph()

if __name__ == "__main__":
    print(graph.get_graph(xray=True).draw_mermaid())

