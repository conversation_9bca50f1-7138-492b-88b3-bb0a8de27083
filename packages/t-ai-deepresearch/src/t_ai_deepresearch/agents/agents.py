# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from langgraph.prebuilt import create_react_agent
from typing import Optional
from langchain_core.runnables import Runnable


from langchain_core.language_models.chat_models import BaseChatModel
from t_ai_deepresearch.config.configuration import Configuration
from t_ai_deepresearch.prompts import apply_prompt_template

# Create agents from agent DSL configuration
def create_agent(
    agentName: str,
    llm: BaseChatModel,
    tools: list,
    prompt_template: str,
    configurable: Configuration
) -> Runnable:
    return create_react_agent(
        name=agentName,
        model=llm,
        tools=tools,
        prompt=lambda state: apply_prompt_template(prompt_template, state, configurable),
    )
