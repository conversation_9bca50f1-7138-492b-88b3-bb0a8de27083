# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
Agent 配置构建器模块

负责从不同来源（JSON 文件、Agent DSL 对象）构建运行时配置。
现在主要用于从 JSON 文件构建配置，运行时配置通过 Configuration 类自动解析和缓存。
"""

import logging
from typing import Dict, Any, List
from t_ai_agent.model.agent_dsl import AgentMeta
from t_ai_app.g import G

logger = logging.getLogger(__name__)


class AgentConfigBuilder:
    """
    统一的 Agent 配置构建器，主要用于从 JSON 文件构建配置。
    运行时配置通过 Configuration 类自动解析和缓存，避免重复解析。
    """
    
    @staticmethod
    def build_config_from_json(studio_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        从 studio_config JSON 构建配置。
        
        Args:
            studio_config: 从 JSON 文件加载的完整配置
            
        Returns:
            包含 agent 对象的配置字典
        """
        # 解析为 AgentDSL
        agent_dict = studio_config.get("agent") or {}
        agent_dsl = AgentMeta.from_dict(agent_dict)
        
        # 现在只需要返回 agent 对象，其他配置通过 Configuration 自动解析
        return {
            "agent": agent_dsl
        }
    
    @staticmethod
    def extract_model_object_from_agent_dsl(agent_dsl):
        """
        从 Agent DSL 对象中提取模型对象。
        直接返回模型对象，避免不必要的序列化/反序列化。
        
        Args:
            agent_dsl: Agent DSL 对象（来自请求）
            
        Returns:
            模型对象或 None
        """
        if hasattr(agent_dsl, 'props') and hasattr(agent_dsl.props, 'model'):
            return agent_dsl.props.model
        return None
    
    # 以下方法保留用于向后兼容，但主要逻辑已移至 Configuration 类
    
    @staticmethod
    def extract_deep_research_config(deep_search_config: Dict[str, Any]) -> Dict[str, Any]:
        """提取深度研究相关配置（向后兼容）"""
        config = {}
        if deep_search_config and deep_search_config.get("enabled", False):
            config["max_plan_iterations"] = deep_search_config.get("maxPlanIterations", 1)
            config["max_step_num"] = deep_search_config.get("maxStepNum", 3) 
            config["max_search_results"] = deep_search_config.get("maxSearchResults", 5)
            config["auto_accepted_plan"] = deep_search_config.get("autoAcceptedPlan", False)
            config["enable_background_investigation"] = deep_search_config.get("enableBackgroundInvestigation", False)
            config["report_style"] = "academic"
        return config
    
    @staticmethod
    def extract_model_config(model_config: Dict[str, Any]) -> Dict[str, Any]:
        """提取模型相关配置（向后兼容）"""
        config = {}
        if model_config:
            config["model_publisher"] = model_config.get("modelPublisher", "bytedance")
            config["model_name"] = model_config.get("name", "doubao-1.5-thinking-pro-250415")
            
            # 模型设置
            model_setting = model_config.get("setting", {})
            if model_setting:
                config["enable_deep_thinking"] = model_setting.get("reasoning_type", "") == "REASONING"
                config["temperature"] = model_setting.get("temperature", 0.0)
                config["top_p"] = model_setting.get("topP", 1.0)
                config["max_tokens"] = model_setting.get("maxTokens", 4000)
        return config
    
    @staticmethod
    def extract_service_config(skill_tools: List[Any]) -> List[Dict[str, Any]]:
        """提取 Service 工具配置（向后兼容）"""
        service_configs = []
        
        for tool in skill_tools or []:
            # 检查是否为 Service 工具类型
            if not (hasattr(tool, 'type') and tool.type == "service"):
                continue
                
            # 提取 service 工具基本信息
            service_config = {
                "key": getattr(tool, 'key', ''),
                "name": getattr(tool, 'name', ''),
                "desc": getattr(tool, 'desc', ''),
                "sse_mode": getattr(tool, 'sse_mode', False),
                "visible": getattr(tool, 'visible', True),
                "final_output": getattr(tool, 'final_output', False),
                "input": getattr(tool, 'input', [])  # 输入参数定义
            }
            
            service_configs.append(service_config)
            logger.info(f"[service-config] Added service tool: {service_config['key']} - {service_config['name']}")
        
        return service_configs
    
    @staticmethod
    def build_service_settings(skill_tools: List[Any], add_to_agents: List[str] = None) -> Dict[str, Any]:
        """构建 service_settings 配置，类似于 mcp_settings 的结构（向后兼容）"""
        if add_to_agents is None:
            add_to_agents = ["researcher"]  # 默认添加 researcher
            
        service_configs = AgentConfigBuilder.extract_service_config(skill_tools)
        
        # 构建类似 mcp_settings 的结构
        service_settings = {
            "enabled": len(service_configs) > 0,
            "services": {},
            "add_to_agents": add_to_agents
        }
        
        for service_config in service_configs:
            service_key = service_config["key"]
            service_settings["services"][service_key] = {
                "name": service_config["name"],
                "desc": service_config["desc"],
                "sse_mode": service_config["sse_mode"],
                "visible": service_config["visible"],
                "final_output": service_config["final_output"],
                "input": service_config["input"],
                "add_to_agents": add_to_agents  # 哪些 agent 可以使用这个 service
            }
        
        logger.info(f"[service-settings] Built service settings with {len(service_configs)} services")
        return service_settings
    
    @staticmethod
    def extract_mcp_config(skill_tools: List[Any]) -> Dict[str, Any]:
        """提取 MCP 工具配置（向后兼容）"""
        mcp_settings = {"servers": {}}
        
        for tool in skill_tools or []:
            # 检查是否为 MCP 工具类型
            if not (hasattr(tool, 'type') and tool.type == "mcp"):
                continue
                
            # 检查是否有 MCP 服务端点配置
            if not hasattr(tool, 'mcp_server_endpoint'):
                continue
                
            # 确定传输方式
            transport = AgentConfigBuilder._determine_transport(tool)
            
            # 提取启用的工具列表和描述
            enabled_tools, tool_descriptions = AgentConfigBuilder._extract_tool_info(tool)
            
            # 根据传输方式生成服务器配置
            server_config = AgentConfigBuilder._build_server_config(
                tool, transport, enabled_tools, tool_descriptions
            )
            
            # 添加到配置中
            mcp_settings["servers"][tool.name] = server_config
            
            # 记录日志
            AgentConfigBuilder._log_mcp_server_config(tool, transport, server_config)
        
        return mcp_settings
    
    @staticmethod
    def _determine_transport(tool) -> str:
        """确定 MCP 工具的传输方式（向后兼容）"""
        # 优先使用用户指定的 transport
        if hasattr(tool, 'transport') and tool.transport:
            return tool.transport
            
        # 根据 source_type 确定默认 transport
        if hasattr(tool, 'source_type'):
            if tool.source_type in ["ai-proxy", "built-in"]:
                return "sse"
                
        # 默认使用 stdio
        return "stdio"
    
    @staticmethod
    def _extract_tool_info(tool) -> tuple[List[str], Dict[str, str]]:
        """提取工具信息：启用的工具列表和描述（向后兼容）"""
        enabled_tools = []
        tool_descriptions = {}
        
        if hasattr(tool, 'tools') and tool.tools:
            for sub_tool in tool.tools:
                enabled_tools.append(sub_tool.key)
                if sub_tool.desc:
                    tool_descriptions[sub_tool.key] = sub_tool.desc
                    
        return enabled_tools, tool_descriptions
    
    @staticmethod
    def _build_server_config(
        tool, 
        transport: str, 
        enabled_tools: List[str], 
        tool_descriptions: Dict[str, str]
    ) -> Dict[str, Any]:
        """构建服务器配置（向后兼容）"""
        base_config = {
            "transport": transport,
            "enabled_tools": enabled_tools,
            "add_to_agents": ["researcher"],
            "tool_descriptions": tool_descriptions,
        }
        
        if transport == "stdio":
            # stdio 模式：解析为 command 和 args
            endpoint_parts = tool.mcp_server_endpoint.split()
            command = endpoint_parts[0] if endpoint_parts else ""
            args = endpoint_parts[1:] if len(endpoint_parts) > 1 else []
            
            base_config.update({
                "command": command,
                "args": args,
            })
        elif transport == "sse":
            # sse 模式：endpoint 是 URL
            base_config.update({
                "url": tool.mcp_server_endpoint,
            })
            
            # 只为 SSE 传输模式添加认证头
            if hasattr(tool, 'source_type') and tool.source_type == "ai-proxy":
                try:
                    auth_token = G.APP_SETTING.ai_proxy.admin_authorization
                    base_config["headers"] = {
                        "Authorization": f"Bearer {auth_token}",
                    }
                    logger.info(f"Added Authorization header for ai-proxy MCP server: {tool.name}")
                except Exception as e:
                    logger.error(f"No authorization found for {tool.name}")
        
        return base_config
    
    @staticmethod
    def _log_mcp_server_config(tool, transport: str, server_config: Dict[str, Any]):
        """记录 MCP 服务器配置日志（向后兼容）"""
        if transport == "stdio":
            command = server_config.get("command", "")
            args = server_config.get("args", [])
            logger.info(f"Added stdio MCP server: {tool.name} with command: {command}, args: {args}")
        elif transport == "sse":
            url = server_config.get("url", "")
            logger.info(f"Added SSE MCP server: {tool.name} with URL: {url}")
    
    # 以下方法保留用于向后兼容，但建议新代码使用 Configuration 类
    
    @staticmethod
    def build_research_config_from_agent_dsl(agent_dsl) -> Dict[str, Any]:
        """
        从 Agent DSL 对象构建深度研究配置（向后兼容）。
        注意：新代码建议使用 Configuration 类，这个方法保留是为了兼容现有代码。
        
        Args:
            agent_dsl: Agent DSL 对象（来自请求）
            
        Returns:
            包含深度研究配置的字典
        """
        config = {}
        config["agent"] = agent_dsl
        if hasattr(agent_dsl, 'props') and agent_dsl.props is not None:
            props = agent_dsl.props
            
            # 确保 props 是正确的对象类型，而不是字典
            if hasattr(props, 'get'):
                return config
            
            # 提取深度研究配置
            if hasattr(props, 'deepResearch') and props.deepResearch:
                deep_search_dict = {
                    "enabled": getattr(props.deepResearch, 'enabled', False),
                    "maxPlanIterations": getattr(props.deepResearch, 'max_plan_iterations', 1),
                    "maxStepNum": getattr(props.deepResearch, 'max_step_num', 3),
                    "maxSearchResults": getattr(props.deepResearch, 'max_search_results', 5),
                    "autoAcceptedPlan": getattr(props.deepResearch, 'auto_accepted_plan', False),
                    "enableBackgroundInvestigation": getattr(props.deepResearch, 'enable_background_investigation', False)
                }
                config.update(AgentConfigBuilder.extract_deep_research_config(deep_search_dict))
            
            # 提取模型配置中影响节点行为的关键参数
            if hasattr(props, 'model') and props.model:
                model_dict = props.model.model_dump() if hasattr(props.model, 'model_dump') else {}
                # 只提取影响节点逻辑的关键配置
                model_config = AgentConfigBuilder.extract_model_config(model_dict)
                # 只保留 enable_deep_thinking，其他模型参数通过 model_obj 传递
                if "enable_deep_thinking" in model_config:
                    config["enable_deep_thinking"] = model_config["enable_deep_thinking"]
            
            # 提取 MCP 工具配置：直接传递 Pydantic 模型对象，供属性访问
            if hasattr(props, 'skill_tools') and props.skill_tools:
                config["mcp_settings"] = AgentConfigBuilder.extract_mcp_config(list(props.skill_tools))
                # 提取 Service 工具配置并构建 service_settings
                config["service_settings"] = AgentConfigBuilder.build_service_settings(list(props.skill_tools))
        
        # 提取 agent 名称
        config["main_agent_name"] = getattr(agent_dsl, 'name', 'Default Agent')
        
        return config
    
    @staticmethod
    def build_config_from_agent_dsl(agent_dsl) -> Dict[str, Any]:
        """
        从 Agent DSL 对象构建完整配置（向后兼容方法）。
        注意：这个方法保留是为了兼容现有代码，新代码建议使用 Configuration 类。
        
        Args:
            agent_dsl: Agent DSL 对象（来自请求）
            
        Returns:
            包含运行时配置的字典
        """
        config = {}
        
        if hasattr(agent_dsl, 'props'):
            props = agent_dsl.props
            
            # 提取深度研究配置
            if hasattr(props, 'deepResearch') and props.deepResearch:
                deep_search_dict = {
                    "enabled": getattr(props.deepResearch, 'enabled', False),
                    "maxPlanIterations": getattr(props.deepResearch, 'max_plan_iterations', 1),
                    "maxStepNum": getattr(props.deepResearch, 'max_step_num', 3),
                    "maxSearchResults": getattr(props.deepResearch, 'max_search_results', 5),
                    "autoAcceptedPlan": getattr(props.deepResearch, 'auto_accepted_plan', False),
                    "enableBackgroundInvestigation": getattr(props.deepResearch, 'enable_background_investigation', False)
                }
                config.update(AgentConfigBuilder.extract_deep_research_config(deep_search_dict))
            
            # 提取模型配置
            if hasattr(props, 'model') and props.model:
                model_dict = props.model.model_dump() if hasattr(props.model, 'model_dump') else {}
                config.update(AgentConfigBuilder.extract_model_config(model_dict))
            
            # 提取 MCP 工具配置：直接传递 Pydantic 模型对象，供属性访问
            if hasattr(props, 'skill_tools') and props.skill_tools:
                config["mcp_settings"] = AgentConfigBuilder.extract_mcp_config(list(props.skill_tools))
        
        # 提取 agent 名称
        config["main_agent_name"] = getattr(agent_dsl, 'name', 'Default Agent')
        
        return config
