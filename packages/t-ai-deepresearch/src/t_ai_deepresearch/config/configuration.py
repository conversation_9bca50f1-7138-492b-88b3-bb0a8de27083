# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import logging
import os
import json
from pathlib import Path
from dataclasses import dataclass, field, fields
from typing import Any, Optional, Dict
from collections import OrderedDict
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.runnables import RunnableConfig

from t_ai_agent.model.agent_dsl import Agent<PERSON><PERSON>
from t_ai_deepresearch.config.report_style import ReportStyle

logger = logging.getLogger(__name__)



@dataclass(kw_only=True)
class Configuration:
    """The configurable fields."""

    resources: list[dict] = field(default_factory=list)  # Resources to be used for the research
    agent: Optional[AgentMeta] = None  # 主要的 agent 配置对象
    thread_id: Optional[str] = None
    model_obj: Optional[Any] = None
    
    # 缓存解析后的配置值
    _parsed_config: Dict[str, Any] = field(default_factory=dict, init=False)
    
    def __post_init__(self):
        """初始化后自动解析 agent 配置"""
        if self.agent:
            self._parse_agent_config()
    
    def _parse_agent_config(self):
        """解析 agent 对象并缓存配置值"""
        if not self.agent or not hasattr(self.agent, 'props') or not self.agent.props:
            return
            
        props = self.agent.props
        
        # 解析深度研究配置
        if hasattr(props, 'deepResearch') and props.deepResearch:
            deep_research = props.deepResearch
            self._parsed_config.update({
                'max_plan_iterations': getattr(deep_research, 'max_plan_iterations', 1),
                'max_step_num': getattr(deep_research, 'max_step_num', 3),
                'max_search_results': getattr(deep_research, 'max_search_results', 3),
                'auto_accepted_plan': getattr(deep_research, 'auto_accepted_plan', False),
                'enable_background_investigation': getattr(deep_research, 'enable_background_investigation', False),
                'report_style': ReportStyle.ACADEMIC.value
            })
        
        # 解析模型配置
        if hasattr(props, 'model') and props.model:
            model = props.model
            if hasattr(model, 'setting') and model.setting:
                setting = model.setting
                self._parsed_config.update({
                    'enable_deep_thinking': getattr(setting, 'reasoning_type', '') == 'REASONING',
                    'temperature': getattr(setting, 'temperature', 0.0),
                    'top_p': getattr(setting, 'topP', 1.0),
                    'max_tokens': getattr(setting, 'maxTokens', 4000)
                })
        
        # 解析 MCP 和 Service 配置
        if hasattr(props, 'skill_tools') and props.skill_tools:
            try:
                from t_ai_deepresearch.config.agent_config_builder import AgentConfigBuilder
                self._parsed_config.update({
                    'mcp_settings': AgentConfigBuilder.extract_mcp_config(list(props.skill_tools)),
                    'service_settings': AgentConfigBuilder.build_service_settings(list(props.skill_tools))
                })
            except ImportError as e:
                logger.warning(f"无法导入 AgentConfigBuilder: {e}")
                self._parsed_config.update({
                    'mcp_settings': {},
                    'service_settings': {}
                })
        
        # 解析 agent 名称
        self._parsed_config['main_agent_name'] = getattr(self.agent, 'name', 'Default Agent')
        
        logger.info(f"Parsed agent config: {list(self._parsed_config.keys())}")
    
    # 属性访问器，从缓存中获取值
    @property
    def max_plan_iterations(self) -> int:
        """从 agent 对象获取最大计划迭代次数"""
        return self._parsed_config.get('max_plan_iterations', 1)
    
    @property
    def max_step_num(self) -> int:
        """从 agent 对象获取最大步骤数"""
        return self._parsed_config.get('max_step_num', 3)
    
    @property
    def max_search_results(self) -> int:
        """从 agent 对象获取最大搜索结果数"""
        return self._parsed_config.get('max_search_results', 3)
    
    @property
    def auto_accepted_plan(self) -> bool:
        """从 agent 对象获取是否自动接受计划"""
        return self._parsed_config.get('auto_accepted_plan', False)
    
    @property
    def enable_background_investigation(self) -> bool:
        """从 agent 对象获取是否启用后台调查"""
        return self._parsed_config.get('enable_background_investigation', False)
    
    @property
    def enable_deep_thinking(self) -> bool:
        """从 agent 对象获取是否启用深度思考"""
        return self._parsed_config.get('enable_deep_thinking', False)
    
    @property
    def main_agent_name(self) -> str:
        """从 agent 对象获取主 agent 名称"""
        return self._parsed_config.get('main_agent_name', 'Default Agent')
    
    @property
    def mcp_settings(self) -> dict:
        """从 agent 对象获取 MCP 设置"""
        return self._parsed_config.get('mcp_settings', {})
    
    @property
    def service_settings(self) -> dict:
        """从 agent 对象获取 Service 设置"""
        return self._parsed_config.get('service_settings', {})
    
    @property
    def report_style(self) -> str:
        """从 agent 对象获取报告风格，默认为学术风格"""
        return self._parsed_config.get('report_style', ReportStyle.ACADEMIC.value)
    
    def update_agent(self, new_agent: AgentMeta):
        """更新 agent 对象并重新解析配置"""
        self.agent = new_agent
        self._parsed_config.clear()
        self._parse_agent_config()
    
    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> "Configuration":
        """
        Create a Configuration instance from a RunnableConfig.
        如果配置项在 RunnableConfig 中不存在，会尝试从 JSON 配置文件加载默认值。
        """
        configurable = (
            config["configurable"] if config and "configurable" in config else {}
        )
        
        # 仅在 Studio 模式下尝试从 workflow 模块获取 JSON 配置默认值
        json_defaults = {}
        if _is_studio_mode():
            try:
                from t_ai_deepresearch.workflow import get_json_config_defaults
                json_defaults = get_json_config_defaults()
            except (ImportError, Exception) as e:
                logger.debug(f"无法获取 JSON 配置默认值: {e}")
        
        values: dict[str, Any] = {}
        for f in fields(cls):
            if not f.init:
                continue
                
            # 优先级：环境变量 > RunnableConfig > (Studio) JSON 配置 > 字段默认值
            env_value = os.environ.get(f.name.upper())
            config_value = configurable.get(f.name)
            json_value = json_defaults.get(f.name)
            
            if env_value is not None:
                values[f.name] = env_value
            elif config_value is not None:
                values[f.name] = config_value
            elif json_value is not None:
                values[f.name] = json_value

        
        return cls(**{k: v for k, v in values.items() if v is not None})
    
    @staticmethod
    def _cache_key(thread_id: Optional[str]) -> Optional[str]:
        return thread_id if thread_id else None

    @classmethod
    def register_llm(cls, thread_id: str, llm: BaseChatModel) -> None:
        """将 LLM 实例注册到缓存，按线程复用。"""
        key = cls._cache_key(thread_id)
        if not key:
            return
        # LRU: 若存在先删除再插入到末尾
        if key in _LLM_CACHE:
            _LLM_CACHE.pop(key, None)
        _LLM_CACHE[key] = llm
        # 超限清理
        max_size = _get_max_cache_size()
        while len(_LLM_CACHE) > max_size:
            _LLM_CACHE.popitem(last=False)

    @classmethod
    def unregister_llm(cls, thread_id: str) -> None:
        """显式移除某个线程的 LLM，释放资源。"""
        key = cls._cache_key(thread_id)
        if key and key in _LLM_CACHE:
            _LLM_CACHE.pop(key, None)

    def get_llm(self) -> Optional[BaseChatModel]:
        """
        获取已缓存的 LLM 实例；若不存在，尝试返回 JSON 默认配置中的 LLM。
        同步节点应优先调用本方法。
        """
        effective_thread_id = "__studio__" if _is_studio_mode() else self.thread_id
        logger.info(f"get_llm: {effective_thread_id}")
        key = self._cache_key(effective_thread_id)
        if key and key in _LLM_CACHE:
            # 触发 LRU 更新
            llm = _LLM_CACHE.pop(key)
            _LLM_CACHE[key] = llm
            return llm
        return None

    async def aget_llm(self) -> Optional[BaseChatModel]:
        """
        异步获取（必要时创建）LLM 实例：
        - 若缓存命中，直接返回
        - 若传入了 model_obj，则按需创建并写入缓存
        - 否则回退到 JSON 默认配置
        """
        effective_thread_id = "__studio__" if _is_studio_mode() else self.thread_id
        key = self._cache_key(effective_thread_id)
        if key and key in _LLM_CACHE:
            llm = _LLM_CACHE.pop(key)
            _LLM_CACHE[key] = llm
            return llm

        # 惰性创建
        if self.model_obj is not None:
            try:
                from t_ai_deepresearch.llms.llm import create_llm_from_agent_model
                llm = await create_llm_from_agent_model(self.model_obj)
                if key:
                    _LLM_CACHE[key] = llm
                return llm
            except Exception as e:
                logger.warning(f"创建 LLM 失败，将回退到默认 LLM: {e}")

        # Studio 模式：按需从 model_config 创建一次并缓存
        if _is_studio_mode():
            try:
                from t_ai_deepresearch.workflow import get_json_config_defaults, create_simple_llm_from_config
                config_defaults = get_json_config_defaults()
                model_conf = config_defaults.get("model_config") if isinstance(config_defaults, dict) else None
                if model_conf:
                    llm = create_simple_llm_from_config(model_conf)
                    if effective_thread_id:
                        Configuration.register_llm(effective_thread_id, llm)
                    return llm
            except Exception as e:
                logger.warning(f"无法按需创建默认 LLM 实例: {e}")
        return None


def get_recursion_limit(default: int = 25) -> int:
    """Get the recursion limit from environment variable or use default.

    Args:
        default: Default recursion limit if environment variable is not set or invalid

    Returns:
        int: The recursion limit to use
    """
    try:
        env_value_str = os.getenv("AGENT_RECURSION_LIMIT", str(default))
        parsed_limit = int(env_value_str)

        if parsed_limit > 0:
            logger.info(f"Recursion limit set to: {parsed_limit}")
            return parsed_limit
        else:
            logger.warning(
                f"AGENT_RECURSION_LIMIT value '{env_value_str}' (parsed as {parsed_limit}) is not positive. "
                f"Using default value {default}."
            )
            return default
    except ValueError:
        raw_env_value = os.getenv("AGENT_RECURSION_LIMIT")
        logger.warning(
            f"Invalid AGENT_RECURSION_LIMIT value: '{raw_env_value}'. "
            f"Using default value {default}."
        )
        return default


_LLM_CACHE: "OrderedDict[str, BaseChatModel]" = OrderedDict()


def _get_max_cache_size(default: int = 64) -> int:
    try:
        value = int(os.getenv("LLM_CACHE_MAX_SIZE", str(default)))
        return value if value > 0 else default
    except Exception:
        return default


def _is_studio_mode() -> bool:
    val = os.getenv("LANGGRAPH_STUDIO_MODE")
    if val is None:
        val = os.getenv("STUDIO_MODE", "false")
    return str(val).lower() in ("1", "true", "yes")