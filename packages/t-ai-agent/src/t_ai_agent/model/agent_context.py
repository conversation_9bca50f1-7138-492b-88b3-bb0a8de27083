import time
from typing import TYPE_CHECKING, Any, Optional, Union

from agents import ToolCallOutputItem
from agents.mcp import MCPServer
from loguru import logger
from openai.types.responses import (
    ResponseFunctionToolCall,
    ResponseCompletedEvent
)
from pydantic import BaseModel, ConfigDict

from t_ai_app.ctx import ReqCtx
from t_ai_mcp import TMCPServer
from .agent_dsl import AgentMeta, LlmModel, Tools, ServiceTool, HttpTool, McpTool, SubTools, ToolVisible
from .common import TokenUsage
from ..message_i18n import get_i18n_message
from itertools import groupby

from t_ai_agent.agent_tool_visible import get_tool_call_visible, get_tool_call_output_visible

if TYPE_CHECKING:
    from .agent_message import AgentMessage, AgentMessageMeta


class AgentMetadata(BaseModel):
    key: Optional[str] = None
    name: Optional[str] = None
    avatar: Optional[str] = None
    llm_model: Optional[LlmModel] = None

    @classmethod
    def from_meta(cls, **kwargs):
        return cls(**kwargs)


class ToolMetadata(BaseModel):
    key: Optional[str] = None
    name: Optional[str] = None
    module_key: Optional[str] = None
    final_output: bool = False
    tool_config: Optional[Union[ServiceTool, HttpTool, McpTool, SubTools]] = None
    tool_visible: Optional[ToolVisible] = None
    agent_name: Optional[str] = None  # 这里用agent_name，因为sdk中的Agent对象中只有name
    agent_as_tool: Optional[bool] = None  # 为True说明tool是有agent.as_tool而来


class ToolCallMetadata(BaseModel):
    key: Optional[str] = None
    name: Optional[str] = None
    alias_name: Optional[str] = None
    module_key: Optional[str] = None
    agent_name: Optional[str] = None
    arguments: Optional[str] = None
    create_time: Optional[float] = None
    build_args_cost_time: float = 0.00
    invoke_args_changed: bool = False
    invoke_cost_time: Optional[float] = 0.00
    invoke_cost_token: TokenUsage = TokenUsage()
    invoke_status: Optional[str] = "success"
    invoke_output: Optional[str] = None
    tool_visible: Optional[ToolVisible] = None

    def is_successful(self) -> bool:
        return self.invoke_status == "success"

    def cost(self, end_time: float):
        if self.create_time is not None:
            self.invoke_cost_time = round(end_time - self.create_time, 2)
        else:
            self.invoke_cost_time = None

    @classmethod
    def from_meta(cls, key, name, arguments, create_time, module_key=None):
        return cls(
            key=key,
            name=name,
            arguments=arguments,
            create_time=create_time,
            module_key=module_key,
        )


class AgentContext(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True)
    agents: list[AgentMeta] = []
    tools: list[ToolMetadata] = []
    mcp_servers: list[MCPServer] = []
    trigger: Optional[str] = None  # 触发器用于创建Agent实例时工具的重新挂载
    main_agent_lang_switch: Optional[bool] = None  # 主语言开关，用于多语言支持

    def has_any_built_in_mcp(self):
        if len(self.mcp_servers) == 0:
            return False
        for mcp_server in self.mcp_servers:
            if isinstance(mcp_server, TMCPServer):
                return True
        return False

    def get_agent_meta_by_name(self, agent_name: str) -> Optional[AgentMeta]:
        for agent in self.agents:
            if agent.name == agent_name:
                return agent
        return None

    def find_agent_meta(self, agent_key) -> Optional[AgentMeta]:
        for agent in self.agents:
            if agent.key == agent_key:
                return agent
        return None

    def append_agent(self, agent: AgentMeta):
        has_same_key = False
        if len(self.agents) > 0:
            for original_agent in self.agents:
                if original_agent.key == agent.key:
                    has_same_key = True
                    break
        if not has_same_key:
            self.agents.append(agent)
        return self

    def any_tool_set_final_output(self):
        """
        检查是否有任何工具设置了final_output为True
        """
        for tool in self.tools:
            if tool.final_output:
                return True
        return False

    def find_tool(self, tool_key) -> Optional[ToolMetadata]:
        for tool in self.tools:
            if tool.key == tool_key:
                return tool
        return None

    def find_tool_in_agent(self, tool_key, agent_name) -> Optional[ToolMetadata]:
        if agent_name is None:
            return self.find_tool(tool_key)
        for tool in self.tools:
            if tool.key == tool_key and tool.agent_name is not None and tool.agent_name == agent_name:
                return tool
        return None

    def add_tool(
            self,
            tool_key: str,
            tool_name: str,
            module_key: str = "",
            tool_visible: Optional[ToolVisible] = None,
            final_output: bool = False,
            tool_config: Optional[Tools] = None,
            agent_name: Optional[str] = None,
            agent_as_tool: bool | None = None,
    ):
        self.tools.append(
            ToolMetadata(
                key=tool_key,
                name=tool_name,
                module_key=module_key,
                tool_visible=tool_visible,
                final_output=final_output,
                tool_config=tool_config,
                agent_name=agent_name,
                agent_as_tool=agent_as_tool,
            )
        )
        return self

    def add_mcp_server(self, mcp_server: MCPServer):
        self.mcp_servers.append(mcp_server)
        return self


class AgentExecutionContext(AgentContext):
    current_agent: Optional[Any] = None  # Agent对象，来至Agent SDK
    as_tools_call_mapping: dict[str, ToolMetadata] = {}
    tools_call_mapping: dict[str, ToolCallMetadata] = {}
    tools_call_mapping_manual: dict[str, ToolCallMetadata] = {}  # 手动添加进来的工具调用
    cost_time: Optional[float] = 0.00
    variables: dict[str, Any] = {}
    total_token_usage: TokenUsage = TokenUsage()
    agent_result: Optional[Any] = None
    conversation_history: list[dict] = []  # 这个主要存储input_items
    _runner_context: Optional[Any] = None  # 存储Runner的上下文引用
    agent_meta: Optional[AgentMeta] = None
    tools_to_final_output_result: bool = False

    def from_agent_context(self, agent_context: AgentContext):
        self.agents = agent_context.agents
        self.tools = agent_context.tools
        self.mcp_servers = agent_context.mcp_servers
        return self

    def set_conversation_history(self, input_items: list[dict]):
        """
        设置对话历史（主要是input_items）

        Args:
            input_items: 原始的输入消息列表
        """
        self.conversation_history = input_items or []
        return self

    def set_runner_context(self, runner_context):
        """
        设置Runner上下文引用，用于获取完整的对话历史

        Args:
            runner_context: agents库的Runner上下文
        """
        self._runner_context = runner_context
        return self

    def get_conversation_history(self) -> list[dict]:
        """
        获取对话历史（input_items）

        Returns:
            list[dict]: 对话历史列表
        """
        return self.conversation_history

    def get_full_conversation_history(self) -> list[dict]:
        """
        获取完整的对话历史，包括当前会话中的所有消息

        Returns:
            list[dict]: 完整的对话历史列表
        """
        # 尝试从Runner上下文中获取完整的消息历史
        if self._runner_context and hasattr(self._runner_context, "messages"):
            try:
                # 将agents库的消息格式转换为我们的格式
                full_history = []
                for msg in self._runner_context.messages:
                    if hasattr(msg, "role") and hasattr(msg, "content"):
                        # 处理不同类型的消息内容
                        content = ""
                        if isinstance(msg.content, str):
                            content = msg.content
                        elif isinstance(msg.content, list):
                            # 处理多部分内容
                            text_parts = []
                            for part in msg.content:
                                if hasattr(part, "text"):
                                    text_parts.append(part.text)
                                elif isinstance(part, dict) and "text" in part:
                                    text_parts.append(part["text"])
                            content = " ".join(text_parts)
                        elif hasattr(msg.content, "text"):
                            content = msg.content.text

                        full_history.append({"role": msg.role, "content": content})
                return full_history
            except Exception as e:
                logger.warning(f"Failed to get full conversation history from runner context: {e}")

        # 如果无法从Runner获取，则返回input_items
        return self.conversation_history

    def get_input_items_history(self) -> list[dict]:
        """
        明确获取input_items历史（用户输入和部分助手回复）

        Returns:
            list[dict]: input_items历史列表
        """
        return self.conversation_history

    def update_tool_call_status(self, call_id: str, status: str, final_output: str = None):
        """
        Update status for a list of tool calls

        Args:
            call_id (str): List of tool call IDs
            status (str): New status to set ('success' or 'error')
            final_output (str): New final output
        """
        # 修改工具映射
        tool_call_meta = self.tools_call_mapping.get(call_id)
        if tool_call_meta:
            tool_call_meta.cost(time.time())
            tool_call_meta.invoke_status = status
            if final_output:
                tool_call_meta.invoke_output = final_output

        # 修改手动添加的工具映射
        manual_tool_meta = self.tools_call_mapping_manual.get(call_id)
        if manual_tool_meta:
            manual_tool_meta.cost(time.time())
            manual_tool_meta.invoke_status = status
            if final_output:
                manual_tool_meta.invoke_output = final_output

    def create_tool_call_message(self, event_data: ResponseFunctionToolCall) -> Optional["AgentMessage"]:
        """
        Create a tool call message from event data

        Args:
            event_data: The event data containing tool call information

        Returns:
            Optional[AgentMessage]: The created agent message or None if no valid tool call
        """
        from .agent_message import AgentMessage, AgentMessageMeta, AgentHandoffContent

        tools = self.tools
        if not tools:
            return None

        # Check if tool is registered in context
        call_id = event_data.call_id
        tool_key = event_data.name
        arguments = event_data.arguments
        tool_meta: ToolMetadata | None = None

        if len(self.tools) > 0:
            tool_meta = self.find_tool_in_agent(tool_key, self.current_agent.name)

        # If tool info not found in context, log warning
        if tool_meta is None:
            logger.warning(f"The tool call {tool_key} hasn't been initiated yet")
            return None

        # if tool by agent_as_tool
        if tool_meta.agent_as_tool and tool_meta.agent_as_tool is True:
            # 记录一下as_tool的call_id
            self.as_tools_call_mapping[call_id] = tool_meta
            content_text = get_i18n_message(
                "as_tool_handoff_content",
                ReqCtx.get_lang(),
                target_agent_name=tool_meta.name
            )
            handoff_to_agent = self.get_agent_meta_by_name(tool_meta.name) if tool_meta.name else None
            agent_meta = self.get_agent_meta_by_name(tool_meta.agent_name) if tool_meta.agent_name else None
            if handoff_to_agent and agent_meta:
                return AgentMessage(
                    content=AgentHandoffContent(
                        text=content_text,
                        target_agent=AgentMessageMeta.from_agent_meta(handoff_to_agent)
                    ),
                    meta=AgentMessageMeta.from_agent_meta(agent_meta)
                )
            else:
                # 如果无法获取代理元数据，返回None
                return None

        # Create tool call metadata
        self.tools_call_mapping[call_id] = ToolCallMetadata(
            key=tool_key,
            name=tool_meta.name,
            module_key=tool_meta.module_key,
            arguments=arguments,
            tool_visible=tool_meta.tool_visible,
            create_time=time.time(),
        )

        # Create and return agent message
        return AgentMessage.from_event(event_data, content_type="function_call", context=self)

    def create_tool_call_arguments_message(self, data: ToolCallOutputItem) -> Optional["AgentMessage"]:
        from .agent_message import AgentMessage

        tool_call_id = data.raw_item.get("call_id")
        if tool_call_id is None:
            return None

        call_tool_info = self.tools_call_mapping.get(tool_call_id)
        if call_tool_info is None:
            return None

        return AgentMessage.from_event(data, content_type="function_call_arguments", context=self)

    def create_tool_output_message(self, event_item: ToolCallOutputItem) -> Optional[list["AgentMessage"]]:
        """
        Create a tool output message from event item

        Args:
            event_item: The event item containing tool output information

        Returns:
            Optional[list[AgentMessage]]: The created agent message or None if no valid tool output
        """
        from .agent_message import AgentMessage

        # Filter out multiple handoffs messages
        if event_item.output.find("Multiple handoffs detected, ignoring this one.") >= 0:
            return None

        if not event_item.raw_item:
            return None

        tool_call_id = event_item.raw_item.get("call_id")
        if tool_call_id is None:
            return None

        messages = []
        call_tool_info = self.tools_call_mapping.get(tool_call_id)
        if call_tool_info is not None:
            # Update tool call metadata
            call_tool_info.cost(time.time())
            call_tool_info.invoke_output = (
                event_item.output if isinstance(event_item.output, str) else str(event_item.output)
            )

            # Update tool call mapping
            self.tools_call_mapping[tool_call_id] = call_tool_info

            # Create and return agent message
            messages.append(AgentMessage.from_event(event_item, content_type="function_call_output", context=self))

            # 如果工具配置了immediately_output，则立即返回工具输出消息
            tool_metadata = self.find_tool_in_agent(call_tool_info.key, self.current_agent.name)
            if (tool_metadata is not None
                    and tool_metadata.tool_config is not None
                    and tool_metadata.tool_config.is_immediately_output()
            ):
                messages.append(
                    AgentMessage.from_event(call_tool_info.invoke_output, content_type="text", context=self))
        return messages

    def update_tool_call_arguments(self, call_id: str, arguments: str):
        """
        Update tool call arguments where invoke trantor programmable service args has ModuleKey

        :param call_id:
        :param arguments:
        :return:
        """
        # 修改工具映射
        tool_meta = self.tools_call_mapping.get(call_id)
        if tool_meta:
            tool_meta.arguments = arguments
            tool_meta.invoke_args_changed = True

        # 修改手动添加的工具映射 - 使用 .get() 方法安全访问
        manual_tool_meta = self.tools_call_mapping_manual.get(call_id)
        if manual_tool_meta:
            manual_tool_meta.arguments = arguments
            manual_tool_meta.invoke_args_changed = True

        # 此处需要更新 conversation_history 里对应的工具调用参数
        conversation_history = self.get_conversation_history()
        for item in conversation_history:
            if item.get("call_id") == call_id:
                item["arguments"] = arguments
                break

    def manual_add_tool_call_mapping(self, call_id, tool_call: ToolCallMetadata, agent_name: str):
        if self.tools_call_mapping.get(call_id) is None:
            tool_key = tool_call.key
            if len(self.tools) > 0:
                tool_info = self.find_tool_in_agent(tool_key, agent_name)
                if tool_info is not None and tool_info.name is not None:
                    tool_call.name = tool_info.name
                    tool_call.module_key = tool_info.module_key
                    tool_call.agent_name = agent_name
                    tool_call.tool_visible = tool_info.tool_visible
            self.tools_call_mapping_manual[call_id] = tool_call

    def create_tool_call_messages_with_manual(self, data: ToolCallOutputItem) -> list["AgentMessage"]:
        """
        创建手动设置进来的工具消息
        :return: 消息列表
        """
        from .agent_message import (
            AgentMessage,
            AgentMessageMeta,
            AgentToolCallContent,
            AgentToolOutputContent,
            AgentHandoffContent,
        )

        messages = []

        as_tool_source_agent_name = None
        as_tool_agent_name = None
        as_tool_call_id = data.raw_item.get("call_id") or ''
        as_tool_metadata = self.as_tools_call_mapping.get(as_tool_call_id)
        if as_tool_metadata is not None:
            as_tool_agent_name = as_tool_metadata.name
            as_tool_source_agent_name = as_tool_metadata.agent_name

        as_tool_agent_message_meta = None
        if as_tool_agent_name:
            as_tool_agent_meta = self.get_agent_meta_by_name(as_tool_agent_name)
            if as_tool_agent_meta:
                as_tool_agent_message_meta = AgentMessageMeta.from_agent_meta(as_tool_agent_meta)

        # grouped 就是 {'agentA': [(call_id, ToolCallMetadata), ...], 'agentB': [...]}
        grouped = {
            agent: list(items)
            for agent, items in groupby(
                sorted(self.tools_call_mapping_manual.items(), key=lambda m: m[1].agent_name or ""),
                key=lambda m: m[1].agent_name or ""
            )
        }

        for agent_name, items in grouped.items():
            message_meta = None
            tool_agent_meta = self.get_agent_meta_by_name(agent_name)
            if tool_agent_meta:
                message_meta = AgentMessageMeta.from_agent_meta(tool_agent_meta)

            # 如果agent_name和 as_tool_agent_name不同，则发送一下转交信息
            if agent_name and as_tool_agent_message_meta and agent_name != as_tool_agent_name and message_meta:
                content_text = get_i18n_message(
                    "as_tool_handoff_content",
                    ReqCtx.get_lang(),
                    target_agent_name=agent_name
                )
                handoff_message = AgentMessage(
                    content=AgentHandoffContent(text=content_text, target_agent=message_meta),
                    meta=as_tool_agent_message_meta
                )
                messages.append(handoff_message)

            # 工具消息 和 工具输出消息
            for call_id, tool_call in items:
                tool_call_visible = get_tool_call_visible(tool_call.tool_visible)
                call_message = AgentMessage(
                    content=AgentToolCallContent(
                        key=tool_call.key,
                        name=tool_call.name,
                        arguments=tool_call.arguments,
                        call_id=call_id,
                        visible=tool_call_visible,
                    ),
                    meta=message_meta,
                )
                messages.append(call_message)

                tool_output_visible = get_tool_call_output_visible(tool_call.tool_visible)
                output_message = AgentMessage(
                    content=AgentToolOutputContent(
                        call_id=call_id,
                        output=tool_call.invoke_output,
                        invoke_status=tool_call.invoke_status,
                        invoke_cost_time=tool_call.invoke_cost_time,
                        visible=tool_output_visible,
                    ),
                    meta=message_meta,
                )
                messages.append(output_message)

                # 如果工具配置了immediately_output，则立即返回工具输出消息
                tool_metadata = self.find_tool_in_agent(tool_call.key, agent_name)
                if tool_metadata and tool_metadata.tool_config and tool_metadata.tool_config.is_immediately_output():
                    messages.append(
                        AgentMessage.from_event(
                            tool_call.invoke_output,
                            content_type="text",
                            context=self,
                            meta=message_meta)
                    )

            # 如果agent_name和 as_tool_agent_name不同，则转交回去
            if agent_name and as_tool_agent_message_meta and agent_name != as_tool_agent_name:
                content_text = get_i18n_message(
                    "as_tool_handoff_feedback_content",
                    ReqCtx.get_lang(),
                    target_agent_name=as_tool_agent_name
                )
                handoff_message = AgentMessage(
                    content=AgentHandoffContent(
                        text=content_text,
                        target_agent=as_tool_agent_message_meta
                    ),
                    meta=message_meta
                )
                messages.append(handoff_message)

        # 交接给主
        if as_tool_source_agent_name and as_tool_agent_message_meta:
            as_tool_source_agent_meta = self.get_agent_meta_by_name(as_tool_source_agent_name)
            if as_tool_source_agent_meta:
                as_tool_source_agent_message_meta = AgentMessageMeta.from_agent_meta(as_tool_source_agent_meta)
                content_text = get_i18n_message(
                    "as_tool_handoff_feedback_content",
                    ReqCtx.get_lang(),
                    target_agent_name=as_tool_source_agent_name
                )
                handoff_message = AgentMessage(
                    content=AgentHandoffContent(text=content_text, target_agent=as_tool_source_agent_message_meta),
                    meta=as_tool_agent_message_meta
                )
                messages.append(handoff_message)

        # 清空掉手动添加的工具
        self.tools_call_mapping_manual.clear()

        return messages

    def update_token_usage(self, event_data: ResponseCompletedEvent):
        """
        Handle response completion event and update token usage

        Args:
            event_data: The response completion event data

        """
        if not event_data.response:
            return

        invoke_response = event_data.response
        if not invoke_response.usage:
            return

        # Update total token usage
        usage = invoke_response.usage
        if usage:
            self.total_token_usage.add(usage)

    def create_token_usage_message(self) -> "AgentMessage":
        """
        Get a message containing the current token usage

        Returns:
            AgentMessage: Message containing token usage information
        """
        from .agent_message import AgentMessage

        return AgentMessage.from_event(self.total_token_usage, content_type="tokens", context=self)

    def create_callback_message(self) -> Optional["AgentMessage"]:
        from .agent_message import AgentMessage

        agent_meta = self.get_agent_meta_by_name(getattr(self.current_agent, 'name'))

        # 用户问题建议回调
        if agent_meta and agent_meta.props.user_questions_suggest:

            header = ReqCtx.get_header() or {}
            callback_url = header.t_ai_callback or ""
            return AgentMessage.from_event(
                {
                    "callback_url": f"{callback_url}/api/trantor/ai/recommend-prompt",
                    "callback_type": "http",
                },
                content_type="callback",
                context=self,
            )
        else:
            return None
