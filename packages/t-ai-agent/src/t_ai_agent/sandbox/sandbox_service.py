from typing import Any
from .py_sandbox import PythonSandbox
from .model import SandboxRequest


async def run_sandbox(request: SandboxRequest) -> Any:
    """
    执行Python代码沙箱
    """
    code = request.code
    arguments = request.arguments

    # 创建沙箱实例
    sandbox = PythonSandbox()

    # 执行Python代码，传递参数
    execution_result = sandbox.run_code(code, arguments)
    
    # 返回执行结果
    return execution_result.to_dict()
