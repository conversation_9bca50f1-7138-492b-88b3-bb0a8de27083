"""
Sandbox module for dynamically executing Python code.
This module provides functionality to safely execute Python code within a controlled environment.
"""

import sys
import io
import os
import tempfile
import uuid
import traceback
import time
import resource
from typing import Dict, Any, Tuple, Optional, List, Union
import ast
import contextlib
import json


class CodeExecutionResult:
    """
    Class to hold the results of code execution for API responses.
    """
    
    def __init__(
        self,
        success: bool,
        result: Any = None,
        output: str = "",
        error: str = "",
        execution_time: float = 0.0,
        temp_file: str = None,
    ):
        """
        Initialize the execution result.
        
        Args:
            success: Whether the execution was successful
            result: The return value of the execution if any
            output: The stdout/stderr output
            error: Error message if execution failed
            execution_time: Time taken to execute the code in seconds
            temp_file: Path to the temporary file if code was saved to disk
        """
        self.success = success
        self.result = result
        self.output = output
        self.error = error
        self.execution_time = execution_time
        self.temp_file = temp_file
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the execution result to a dictionary for API responses."""
        return {
            "success": self.success,
            "result": self.result,
            "output": self.output,
            "error": self.error,
            "execution_time": self.execution_time,
            "temp_file": self.temp_file,
        }
    
    def to_json(self) -> str:
        """Convert the execution result to a JSON string for API responses."""
        # Use a custom serializer to handle non-serializable objects
        return json.dumps(
            self.to_dict(),
            default=lambda o: str(o) if not isinstance(o, (dict, list, str, int, float, bool, type(None))) else o,
            ensure_ascii=False,
        )


class PythonSandbox:
    """
    A sandbox environment for executing Python code dynamically with controlled access to resources.
    """
    
    # Default list of allowed modules for safe execution
    DEFAULT_ALLOWED_MODULES = [
        "math", "random", "time", "datetime", "collections", "itertools", "functools", "re", "json", "heapq",
        "t_ai_agent", "traceback"
    ]
    
    # Default time limit for code execution (in seconds)
    DEFAULT_TIME_LIMIT = 5.0
    
    # Default memory limit for code execution (in bytes, 100MB)
    DEFAULT_MEMORY_LIMIT = 100 * 1024 * 1024
    
    def __init__(
        self, 
        allowed_modules: Optional[List[str]] = None,
        time_limit: float = DEFAULT_TIME_LIMIT,
        memory_limit: int = DEFAULT_MEMORY_LIMIT,
        temp_dir: Optional[str] = None
    ):
        """
        Initialize the sandbox with optional restrictions.
        
        Args:
            allowed_modules: List of module names that are allowed to be imported.
                            If None, the default allowed modules list is used.
            time_limit: Maximum execution time in seconds
            memory_limit: Maximum memory usage in bytes
            temp_dir: Directory to store temporary files. If None, system temp dir is used.
        """
        self.allowed_modules = allowed_modules or self.DEFAULT_ALLOWED_MODULES
        self.time_limit = time_limit
        self.memory_limit = memory_limit
        self.temp_dir = temp_dir or tempfile.gettempdir()
        self.locals_dict: Dict[str, Any] = {}
        self.globals_dict: Dict[str, Any] = {}
        
        # Create sandbox directory if it doesn't exist
        self.sandbox_dir = os.path.join(self.temp_dir, "python_sandbox")
        os.makedirs(self.sandbox_dir, exist_ok=True)
        
        # Initialize the global dictionary with safe builtins
        self._setup_environment()
    
    def _setup_environment(self):
        """Set up the execution environment with necessary builtins and restrictions."""
        # Start with a fresh globals dictionary with restricted builtins
        import builtins
        
        # Create a safe subset of builtins
        safe_builtins = {}
        for name in dir(builtins):
            if name not in [
                'open', 'eval', 'exec', 'compile',
                'input', 'memoryview', 'breakpoint', 'globals'
                # 注意：允许 __import__ 函数以支持模块导入
            ]:
                safe_builtins[name] = getattr(builtins, name)
        
        self.globals_dict = {
            '__builtins__': safe_builtins,
        }
        
        # Add allowed modules to the globals
        for module_name in self.allowed_modules:
            try:
                if module_name == "t_ai_agent":
                    # 对项目内部模块的特殊处理
                    import t_ai_agent
                    self.globals_dict[module_name] = t_ai_agent
                else:
                    # 导入普通模块
                    module = __import__(module_name)
                    self.globals_dict[module_name] = module
            except ImportError:
                # Skip modules that can't be imported
                pass
        
        # Initialize an empty locals dictionary
        self.locals_dict = {}
    
    def _is_safe_code(self, code_str: str) -> Tuple[bool, str]:
        """
        Analyze the code to check for potentially unsafe operations.
        
        Args:
            code_str: The code string to analyze
            
        Returns:
            Tuple containing:
                - Boolean indicating if the code is safe
                - Error message if the code is unsafe, empty string otherwise
        """
        try:
            # Parse the code into an AST
            tree = ast.parse(code_str)
            
            print(f"安全检查: 开始分析代码安全性")
            
            # Check for potentially dangerous operations
            for node in ast.walk(tree):
                # Check for imports outside the allowed list
                if isinstance(node, (ast.Import, ast.ImportFrom)):
                    # 处理 ImportFrom 语句
                    if isinstance(node, ast.ImportFrom):
                        module = node.module or ""
                        root_module = module.split('.')[0] if module else ""
                        print(f"检查从模块导入: {module} (根模块: {root_module})")
                        
                        # 如果是从 t_ai_agent 模块导入，则允许
                        if root_module == "t_ai_agent":
                            print(f"允许从 t_ai_agent 导入: {module}")
                            # 跳过名称检查，因为是从 t_ai_agent 导入的子模块或函数
                            continue
                        
                        # 检查模块是否在允许列表中
                        if root_module and root_module not in self.allowed_modules:
                            print(f"不允许从该模块导入: {root_module}")
                            return False, f"Import from module '{root_module}' is not allowed"
                    
                    # 检查直接导入的模块名
                    for name in node.names:
                        module_name = name.name.split('.')[0]
                        print(f"检查导入模块名: {name.name} (根模块: {module_name})")
                        
                        # 如果导入的是 t_ai_agent 或其子模块，则允许
                        if module_name == "t_ai_agent":
                            print(f"允许导入 t_ai_agent 模块: {name.name}")
                            continue
                            
                        # 其他模块必须在允许列表中
                        if module_name not in self.allowed_modules:
                            print(f"模块不在允许列表中: {module_name}")
                            return False, f"Import of module '{module_name}' is not allowed"
                        else:
                            print(f"模块允许导入: {module_name}")
                
                # Check for file operations
                if isinstance(node, ast.Call):
                    func = node.func
                    if isinstance(func, ast.Name) and func.id == 'open':
                        return False, "File operations are not allowed"
                    
                    # Check for eval/exec
                    if isinstance(func, ast.Name) and func.id in ['eval', 'exec']:
                        return False, "eval() and exec() functions are not allowed"
                
                # Check for os/sys operations that might be dangerous
                if isinstance(node, ast.Attribute):
                    if isinstance(node.value, ast.Name):
                        if node.value.id in ['os', 'sys'] and node.attr.startswith('_'):
                            return False, f"Access to '{node.value.id}.{node.attr}' is not allowed"
            
            return True, ""
            
        except SyntaxError as e:
            return False, f"Syntax error: {str(e)}"
    
    def save_code_to_file(self, code_str: str, filename: str = None) -> str:
        """
        Save the provided code to a file in the t-ai-code-sandbox directory.
        
        Args:
            code_str: The Python code to save
            filename: Optional custom filename, if None a UUID will be generated
            
        Returns:
            Path to the saved file
        """
        # Define the sandbox directory
        sandbox_dir = "/Users/<USER>/Development/Repositories/terminus/t-ai2/packages/t-ai-code-sandbox"
        
        # Ensure the directory exists
        os.makedirs(sandbox_dir, exist_ok=True)
        
        # Generate a filename if not provided
        if not filename:
            filename = f"sandbox_{uuid.uuid4().hex}.py"
        elif not filename.endswith('.py'):
            filename = f"{filename}.py"
            
        filepath = os.path.join(sandbox_dir, filename)
        
        # Save the code to the file
        with open(filepath, 'w') as f:
            f.write(code_str)
        
        return filepath
    
    def _limit_resources(self):
        """Set resource limits for the execution."""
        # Set CPU time limit (convert to integer for resource.setrlimit)
        cpu_limit = int(self.time_limit)
        resource.setrlimit(resource.RLIMIT_CPU, (cpu_limit, cpu_limit))
        
        # Set memory limit
        resource.setrlimit(resource.RLIMIT_AS, (self.memory_limit, self.memory_limit))
    
    def run_code(self, code_str: str, arguments: Any = None) -> CodeExecutionResult:
        """
        Execute the provided Python code in the sandbox environment.
        
        Args:
            code_str: The Python code to execute as a string
            arguments: Optional arguments to pass to the code execution context
            
        Returns:
            CodeExecutionResult object containing execution results
        """
        is_safe, error_msg = self._is_safe_code(code_str)
        if not is_safe:
            return CodeExecutionResult(
                success=False,
                error=f"The provided code contains unsafe operations: {error_msg}",
            )
        
        # Capture stdout and stderr
        stdout = io.StringIO()
        stderr = io.StringIO()
        
        # Define variables before the try block
        result = None
        start_time = time.time()
        
        try:
            # Execute the code with captured output
            with contextlib.redirect_stdout(stdout), contextlib.redirect_stderr(stderr):
                # Compile the code
                compiled_code = compile(code_str, "<string>", "exec")
                
                # Make arguments available in the local scope if provided
                if arguments is not None:
                    self.locals_dict['arguments'] = arguments
                
                # Execute the code
                exec(compiled_code, self.globals_dict, self.locals_dict)
                
                # If there's a result variable defined, return it
                if "result" in self.locals_dict:
                    result = self.locals_dict["result"]
                
            # Execution was successful
            output = stdout.getvalue()
            if stderr.getvalue():
                output += "\n--- Errors/Warnings ---\n" + stderr.getvalue()
            
            # Calculate execution time
            execution_time = time.time() - start_time
            
            # Return success, result, and any output
            return CodeExecutionResult(
                success=True,
                result=result,
                output=output,
                execution_time=execution_time,
                temp_file=None,  # No temp file for direct code execution
            )
            
        except Exception as e:
            # Capture the traceback
            error_msg = f"Error: {str(e)}\n{stderr.getvalue()}\n{traceback.format_exc()}"
            execution_time = time.time() - start_time
            return CodeExecutionResult(
                success=False,
                error=error_msg,
                execution_time=execution_time,
                temp_file=None,  # No temp file for direct code execution
            )

    def run_file(self, file_path: str) -> CodeExecutionResult:
        """
        Execute Python code from a file.
        
        Args:
            file_path: Path to the Python file to execute
            
        Returns:
            CodeExecutionResult object containing execution results
        """
        try:
            with open(file_path, 'r') as file:
                code_str = file.read()
            result = self.run_code(code_str)
            result.temp_file = file_path
            return result
        except FileNotFoundError:
            return CodeExecutionResult(success=False, error=f"File not found: {file_path}")
        except Exception as e:
            return CodeExecutionResult(success=False, error=f"Error reading file: {str(e)}")

    def execute_from_api(self, code_str: str, save_to_file: bool = True, filename: str = None) -> CodeExecutionResult:
        """
        Execute code provided through an API, with option to save to file first.
        
        Args:
            code_str: The Python code to execute
            save_to_file: Whether to save the code to a file first
            filename: Optional custom filename (only used if save_to_file is True)
            
        Returns:
            CodeExecutionResult object containing execution results
        """
        if save_to_file:
            # Save the code to a file first
            temp_file = self.save_code_to_file(code_str, filename)
            result = self.run_file(temp_file)
            return result
        else:
            # Execute the code directly without saving to file
            return self.run_code(code_str)
