import time

from httpx import Response
from loguru import logger
import httpx
import httpx_sse

from t_ai_app.ctx import ReqCtx


def get_callback_url(default_url=None):
    header = ReqCtx.get_header()
    return header.t_ai_callback if header else default_url


def get_source_referer_from_header():
    header = ReqCtx.get_header()
    return header.t_ai_source_referer if header else None


def get_source_origin_org_id_from_header():
    header = ReqCtx.get_header()
    return header.t_ai_source_origin_org_id if header else None


def get_source_cookie_from_header():
    header = ReqCtx.get_header()
    return header.t_ai_source_cookie if header else None


def get_trace_id_from_header():
    return ReqCtx.get_trace_id()


def to_trantor_headers() -> dict[str, str]:
    trace_id = ReqCtx.get_trace_id()
    accept_language = ReqCtx.get_header().accept_language

    cookie = ReqCtx.get_header().t_ai_source_cookie
    referer = ReqCtx.get_header().t_ai_source_referer
    authorization = ReqCtx.get_header().t_ai_source_authorization

    origin_org_id = ReqCtx.get_header().t_ai_source_origin_org_id
    tenant_id = ReqCtx.get_header().t_ai_source_tenant_id
    time_zone = ReqCtx.get_header().t_ai_source_time_zone
    trantor2_chain = ReqCtx.get_header().t_ai_source_chain

    team_id = ReqCtx.get_header().t_ai_source_team
    app_id = ReqCtx.get_header().t_ai_source_app
    user_encode = ReqCtx.get_header().t_ai_user_encode
    portal_key = ReqCtx.get_header().t_ai_source_portal

    # 创建基础 headers
    headers = {"Terminus-Request-Id": trace_id}

    if cookie is not None:
        headers["cookie"] = cookie
    else:
        if user_encode is not None:
            headers["trantor2-current-user"] = user_encode
    if referer is not None:
        headers["referer"] = referer
    elif portal_key is not None:
        headers["trantor2-portal-key"] = portal_key
    if authorization is not None:
        headers["authorization"] = authorization
    if origin_org_id is not None:
        headers["trantor2-origin-org-id"] = origin_org_id
    if tenant_id is not None:
        headers["trantor2-tenant-id"] = tenant_id
    if time_zone is not None:
        headers["trantor2-time-zone"] = time_zone
    if accept_language is not None:
        headers["accept-language"] = accept_language
    if trantor2_chain is not None:
        headers["trantor2-chain"] = trantor2_chain
    if team_id is not None:
        headers["trantor2-team"] = team_id
    if app_id is not None:
        headers["trantor2-app"] = app_id

    return headers


def is_var_value(value) -> bool:
    """
    是否是变量
    :param value:
    :return bool:
    """
    if not isinstance(value, dict):
        return False
    var_type = value.get("type", "")
    if var_type == "VarValue" or var_type == "ConstValue" or var_type == "FuncValue" or var_type == "ObjectValue":
        return True
    return False


def is_const_value(value) -> bool:
    """
    是否是常量
    :param value:
    :return bool:
    """
    if not isinstance(value, dict):
        return False
    var_type = value.get("type", "")
    if var_type == "ConstValue":
        return True
    return False


async def get_mcp_built_in_sse_endpoint():
    base_url = ReqCtx.get_header().t_ai_callback
    url = f"{base_url}/api/trantor/mcp/built-in/get-internal-endpoint"

    headers = {"content-type": "application/json"}

    start_time = time.time()
    logger.info(
        "call_service:{} start. url:{}, request:{}, headers:{}", "get_mcp_built_in_sse_endpoint", url, "", headers
    )

    async with httpx.AsyncClient() as client:
        response = await client.get(url, headers=headers, timeout=300.0)
        response.raise_for_status()
        response_json = response.json()

    logger.info("call_service:{} end. response:{}", "get_mcp_built_in_sse_endpoint", response_json)
    logger.debug(f"######## call_service:get_mcp_built_in_sse_endpoint end. cost: {round(time.time() - start_time, 2)}")

    if response_json and response_json.get("data"):
        result = response_json.get("data")
        if result and isinstance(result, dict):
            return result.get("endpoint")
    return None


async def const_value(value: dict):
    return value.get("constValue")


async def dynamic_value(value, variables: dict):
    """
     获取动态变量值，如获取系统变量，模块变量等
    :param value: 变量，可能是常量，可能是动态变量
    :param variables: 请求的入参
    :return: 动态变量值
    """
    if is_var_value(value):
        var_type = value.get("type", "")
        if var_type == "ConstValue":
            # 这个也是常量，就不调用接口了
            return value.get("constValue")
        else:
            base_url = get_callback_url()
            url = f"{base_url}/api/trantor/ai/dynamic-value"

            headers = {"content-type": "application/json", **to_trantor_headers()}
            request = {"value": value, "variables": variables}

            start_time = time.time()
            logger.debug("dynamic_value: start. url:{}, request:{}, headers:{}", url, request, headers)

            async with httpx.AsyncClient() as client:
                response = await client.post(url, headers=headers, json=request, timeout=300.0)
                response.raise_for_status()
                response_json = response.json()

            logger.debug("dynamic_value: end. response:{}", response_json)
            logger.debug(f"######## dynamic_value: end. cost:{round(time.time() - start_time, 2)}")

            return response_json.get("data")
    else:
        return value


async def dynamic_prompt(prompt: str, variables: dict):
    """
     动态替换提示词中的变量
    :param prompt: 提示词
    :param variables: 请求的入参
    :return: 替换后的提示词
    """
    base_url = get_callback_url()
    url = f"{base_url}/api/trantor/ai/dynamic-prompt"

    headers = {"content-type": "application/json", **to_trantor_headers()}
    request = {"prompt": prompt, "variables": variables}

    start_time = time.time()
    logger.debug("dynamic_prompt: start. url:{}, request:{}, headers:{}", url, request, headers)

    async with httpx.AsyncClient() as client:
        response = await client.post(url, headers=headers, json=request, timeout=300.0)
        response.raise_for_status()
        response_json = response.json()

    logger.debug("dynamic_prompt: end. response:{}", response_json)
    logger.debug(f"######## dynamic_prompt: end. cost: {round(time.time() - start_time, 2)}")

    return response_json.get("data")


async def call_service(service_key, params):
    """
     调用服务
    :param service_key: 服务key
    :param params: 请求参数
    :return: 返回结果
    """
    base_url = ReqCtx.get_header().t_ai_callback
    url = f"{base_url}/api/trantor/service/engine/execute/{service_key}"

    headers = {"content-type": "application/json", **to_trantor_headers()}
    request = {
        "params": params,
    }

    start_time = time.time()
    logger.info("call_service:{} start. url:{}, request:{}, headers:{}", service_key, url, request, headers)

    async with httpx.AsyncClient() as client:
        response = await client.post(url, headers=headers, json=request, timeout=300.0)
        if not response.is_success:
            return _handle_error_response(response, response.json())
        response_json = response.json()

    logger.info("call_service:{} end. response:{}", service_key, response_json)
    logger.debug(f"######## call_service:{service_key} end. cost: {round(time.time() - start_time, 2)}")

    if response_json.get("success"):
        if response_json.get("data"):
            result = response_json.get("data")
        else:
            result = {"success": response_json.get("success")}
        return result
    else:
        return _handle_error_response(response, response_json)


async def get_result_validation_config() -> dict:
    base_url = get_callback_url()
    url = f"{base_url}/api/trantor/agent/result-validation-config/get"

    headers = {"content-type": "application/json", **to_trantor_headers()}

    start_time = time.time()
    logger.debug("get_result_validation_config: start. url:{}, headers:{}", url, headers)

    async with httpx.AsyncClient() as client:
        response = await client.get(url, headers=headers, timeout=300.0)
        response.raise_for_status()
        response_json = response.json()

    logger.debug("get_result_validation_config: end. response:{}", response_json)
    logger.debug(f"######## get_result_validation_config: end. cost: {round(time.time() - start_time, 2)}")

    return response_json.get("data")


def _handle_error_response(response: Response, error_data: dict) -> None:
    if error_data.get("err"):
        error_msg = error_data.get("err", {}).get("msg", response.text or "Unknown error")
        error_detail = error_data.get("info", {}).get("innerMsg", "")
    else:
        error_msg = error_data.get("error", response.text or "Unknown error")
        error_detail = response.text
    raise TrantorServiceException(error_msg, error_detail)


async def call_service_sse(service_key, params):
    """
    用于调用支持 SSE 的 Trantor 服务，组装流式数据最终返回的结果。

    :param service_key: 服务key，用于标识要调用的服务
    :param params: 请求参数字典
    :return: 组装后的最终结果，格式与 call_service 保持一致

    异常:
        TrantorServiceException: 当服务调用失败时抛出
    """
    base_url = ReqCtx.get_header().t_ai_callback
    url = f'{base_url}/api/trantor/service/engine/execute/ai-chat/{service_key}'

    headers = to_trantor_headers()
    headers["accept"] = "text/event-stream"

    request_data = {
        'params': params,
    }

    start_time = time.time()
    logger.info('call_service_sse:{} start. url:{}, request:{}, headers:{}', service_key, url, request_data, headers)

    try:
        async with httpx.AsyncClient() as client:
            async with httpx_sse.aconnect_sse(
                client,
                "POST",
                url,
                headers=headers,
                json=request_data,
                timeout=60.0 * 5
            ) as event_source:

                final_result = ""

                async for sse_event in event_source.aiter_sse():
                    if sse_event.data:
                        if sse_event.data == '[DONE]':
                            break

                        final_result += sse_event.data

                if final_result is not None:
                    return final_result
                else:
                    return {
                        "success": False,
                        "data": None
                    }

    except Exception as e:
        logger.error("call_service_sse:{} unexpected error: {}", service_key, str(e))
        raise TrantorServiceException("Service call failed", f"Unexpected error: {str(e)}")
    finally:
        logger.info("call_service_sse:{} end. cost: {}", service_key, round(time.time() - start_time, 2))


class TrantorServiceException(Exception):
    """Trantor 服务调用异常"""

    def __init__(self, message: str, detail: str):
        if detail is not None and len(detail) > 0:
            super().__init__(f"{message} \n detail: {detail}")
        else:
            super().__init__(f"{message}")
