import json
import time
from datetime import datetime

from loguru import logger
from t_ai_agent.ai_proxy_client_factory import ai_proxy_client_factory
from t_ai_agent.model.agent_context import AgentExecutionContext
from t_ai_agent.model.agent_dsl import Agent<PERSON>eta
from t_ai_agent.trantor_meta.model_field_helper import Field<PERSON><PERSON>, build_fields_prompt, build_fields_prompt_by_model_key
from t_ai_agent.utils.json_util import safe_json_loads


# 判断 input_meta 是否包含 ConditionItems、Pageable 或 Model 类型字段
def need_to_expand(input_meta: list[FieldMeta]) -> bool:
    try:
        if not input_meta:
            return False
        for field in input_meta:
            if field is None:
                continue
            if isinstance(field, dict):
                field = FieldMeta.model_validate(field)
            if field.field_type in ["ConditionItems", "Pageable", "Model"]:
                return True
            elif field.field_type == "Object":
                if need_to_expand(field.elements):
                    return True
            elif field.field_type == "Array":
                if need_to_expand([field.element]):
                    return True
        return False
    except Exception as e:
        logger.exception(f"判断是否需要展开字段时发生错误: {e}")
        return False


# 辅助函数：使用大模型映射上下文到参数
async def map_context_to_params(agent_meta: AgentMeta, mapping_prompt):
    """调用大模型API将上下文映射到服务参数，确保结果正确"""
    start_time = time.time()

    system_variables_prompt = f"""# 系统变量:
                        1. {get_current_time_prompt()}"""

    # 构建提示文本，指导模型正确映射
    system_prompt = (
        """你是一个精确的参数映射助手。你的唯一任务是将用户对话上下文准确地映射到服务参数。
                        # 输出要求
                        1. 仅返回符合要求的 JSON 对象，不要包含任何额外解释
                        2. 确保返回的 JSON 与模型字段定义完全匹配
                        3. 确保所有必填字段都有正确的值
                        4. 所有字段值类型必须与字段定义完全匹配

                        # 字段处理规则
                        1. 模型字段统一使用 alias 字段值
                        2. 日期类型字段必须使用日期字符串
                        3. 系统变量可用于获取当前时间等信息
                        4. model_key 存在时必须将其设为对应值
                        5. 如果字段是‘Object/对象’类型，则需要递归处理其子字段，映射后的值需要为 JSON 格式，文本类型的字段优先匹配给子字段里的 name 或 code 字段
                        6. 如果查询条件是关联模型内的字段，conditions 中的 fieldName 值为 a.b.c 格式, eg: 查询采购订单里供应商名称为xxx的数据，则 fieldName 值为 supplier.name，其中 supplier 为采购订单里的字段，字段类型为关联模型对象，关联供应商对象，name 为供应商模型内的字段
                        7. 如果字段是‘Text/文本’类型查询时 operator 使用 CONTAINS

                        # 特殊字段处理
                        ## Pageable 类型
                        {
                            "pageNo": 1,                    // 默认值：1
                            "pageSize": 20,                 // 默认值：20
                            "conditionItems": {             // 必须符合 ConditionItems 结构
                                "type": "ConditionItems",
                                "conditions": {
                                    "fieldName": {          // 使用元数据中的关联模型字段
                                        "operator": "CONTAINS",
                                        "value": "fieldValue"
                                    }
                                },
                                "logicOperator": "AND"
                            },
                            "sortOrders": [{
                                "fieldAlias": "updatedAt", // 排序字段
                                "asc": true               // 排序方式
                            }]
                        }

                        ## ConditionItems 类型
                        {
                            "type": "ConditionItems",
                            "conditions": {
                                "fieldName": {              // 使用元数据中的关联模型字段
                                    "operator": "CONTAINS",
                                    "value": "fieldValue"
                                }
                            },
                            "logicOperator": "AND"          // 默认值：AND
                        }

                        ## 运算符(operator)可选值
                        - 比较运算：EQ(等于), NEQ(不等于), GT(大于), LT(小于), GTE(大等), LTE(小等)
                        - 文本匹配：START_WITH, END_WITH, CONTAINS, NOT_CONTAINS
                        - 空值判断：IS_NULL, IS_NOT_NULL
                        - 集合运算：IN, NOT_IN, NOT_IN_LIST, BETWEEN_AND

                        # 数据操作规则
                        1. 创建操作：不传入 id 字段
                        2. 更新操作：必须传入 id 字段
                        3. 如果 id 在上下文中不存在，禁止随机 id 字段
                        4. 所有写操作都禁止传入以下系统字段：
                        - createdBy, updatedBy
                        - createdAt, updatedAt
                        - version, deleted
                        - requestId, originOrgId, tenantId\n"""
        + system_variables_prompt
    )

    # 确保mapping_prompt是可序列化的
    prompt_str = "以下是对应服务的入参信息、需要映射的字段实际值及系统服务 modelKey，请帮我进行映射：" + json.dumps(
        mapping_prompt, ensure_ascii=False
    )

    try:
        logger.debug(
            f"Provider: {agent_meta.props.model.model_publisher}, Model: {agent_meta.props.model.name}。 开始进行参数映射: {prompt_str}"
        )
        client = await ai_proxy_client_factory.get_client(agent_meta.props.model.model_publisher)

        response = await client.chat.completions.create(
            model=agent_meta.props.model.name,
            messages=[{"role": "system", "content": system_prompt}, {"role": "user", "content": prompt_str}],
            response_format={"type": "json_object"},
            temperature=0.1,  # 使用较低的温度确保更确定性的输出
        )

        # 解析并验证结果
        result_str = response.choices[0].message.content
        mapped_params = safe_json_loads(result_str)

        logger.debug(f"映射结果: {mapped_params}")
        logger.debug(f"参数映射耗时: {round(time.time() - start_time, 2)}秒")

        return mapped_params
    except json.JSONDecodeError as e:
        logger.exception(f"参数映射结果解析失败: {e}")
        logger.error(f"原始响应: {response.choices[0].message.content}")
        raise ValueError("大模型返回的参数格式不正确，无法解析为JSON")
    except Exception as e:
        logger.exception(f"参数映射过程发生错误: {str(e)}")
        raise


# 辅助函数：创建上下文映射提示
def create_context_mapping_prompt(
    sercice_key, model_key, conversation_history, input_metadata, params_str, tool_metadata
):
    # 创建给大模型的提示，用于将agent上下文映射到服务参数
    # 包含服务元数据、模型字段信息和当前agent上下文
    model_fields_meta = None
    if model_key is not None:
        model_fields_meta = build_fields_prompt_by_model_key(model_key, input_metadata)
    else:
        model_fields_meta = build_fields_prompt(input_metadata)

    # model_fields_meta 现在已经是字典列表，不需要额外转换
    prompt = {
        "service_key": sercice_key,
        "input_meta": model_fields_meta,
        "tool_metadata": tool_metadata.model_dump(),
        "user_context": conversation_history,
        "sys_service_model_key": model_key,
        "original_params": params_str,
    }
    # 将 prompt 字典转换为 JSON 字符串
    return json.dumps(prompt, ensure_ascii=False)


def get_current_time_prompt() -> str:
    timestamp = int(datetime.now().timestamp() * 1000)
    return (
        f"# System context\n当前时间是：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}，对应的时间戳是：{timestamp}。"
    )
