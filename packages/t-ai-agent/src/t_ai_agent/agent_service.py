import json
import asyncio
import time
from typing import Any, Dict

from agents import (
    <PERSON>off<PERSON>utputI<PERSON>,
    Runner,
    ToolCallOutputItem,
    HandoffCallItem,
)
from loguru import logger
from openai.types.responses import (
    ResponseCompletedEvent,
    ResponseContentPartDoneEvent,
    ResponseFunctionToolCall,
    ResponseOutputItemAddedEvent,
    ResponseOutputItemDoneEvent,
    ResponseReasoningSummaryPartDoneEvent,
    ResponseReasoningSummaryTextDeltaEvent,
    ResponseTextDeltaEvent,
    EasyInputMessageParam,
)
from sse_starlette.sse import EventSourceResponse, ServerSentEvent
from t_ai_agent.utils.task_evaluation import TaskEvaluation
from t_ai_common.utils.common import obj_pretty, get_service_original_key, get_service_alias_key
from t_ai_mcp.server import MCPServerGuard
from .agent_factory import create_agent, get_agent_from_cache
from .agent_trigger import reset_agent_from_trigger
from .constants import AgentConstants
from .model.agent_context import Agent<PERSON><PERSON><PERSON><PERSON>, AgentExecutionContext
from .model.agent_dsl import <PERSON><PERSON><PERSON>
from .model.agent_message import (
    AgentMessage,
    AgentMessageMeta,
    AgentTextContent,
)
from .utils import input_item_helpers
from .utils.result_evaluation import EvaluationAgent, EvaluationAgentFactory
from .utils.tool_call_cache import (
    cache_tool_call,
    create_cached_tool_call_messages,
    match_tool_call_cache,
)
from .utils.trantor_helper import call_service

SSE_STEAMING_HEADERS = {
    "Content-Type": "text/event-stream",
    "Cache-Control": "no-cache",
    "X-Accel-Buffering": "no",
    "Connection": "keep-alive",
}


async def run(agent_request: Dict[str, Any]) -> Any:
    """
    同步运行 Agent
    """
    input_items = agent_request.get("inputItems")
    variables = agent_request.get("variables", {})
    agent_txt = agent_request.get("agent", {})
    trigger = variables.get("trigger")

    agent_meta = AgentMeta.from_dict(agent_txt)

    if trigger is not None:
        # 如果以触发器的维度来执行则需要移除交接的Agent并且重新生成系统提示词
        reset_agent_from_trigger(agent_meta, trigger)
        starting_agent, agent_ctx = await create_agent(
            agent_meta, AgentContext(trigger=trigger, main_agent_lang_switch=agent_meta.props.reply_with_user_language)
        )
    else:
        starting_agent, agent_ctx = await get_agent_from_cache(agent_meta)
        # 如果有内置的mcp，则不能使用缓存
        if agent_ctx.has_any_built_in_mcp():
            starting_agent, agent_ctx = await create_agent(
                agent_meta,
                AgentContext(main_agent_lang_switch=agent_meta.props.reply_with_user_language)
            )

    # 创建执行上下文
    agent_exec_ctx = AgentExecutionContext(variables=variables)
    agent_exec_ctx.from_agent_context(agent_ctx)
    # 设置对话历史
    agent_exec_ctx.set_conversation_history(input_items)
    # 记录 Agent 元数据
    agent_exec_ctx.agent_meta = agent_meta
    # 设置当前 Agent
    agent_exec_ctx.current_agent = starting_agent

    # 用户输入
    list_input = await input_item_helpers.to_input_items(input_items)

    async with MCPServerGuard(*agent_exec_ctx.mcp_servers):
        agent_result = await Runner.run(
            starting_agent=starting_agent,
            input=list_input,
            context=agent_exec_ctx,
            max_turns=AgentConstants.DEFAULT_MAX_TURNS,
        )

        result = agent_result.final_output
        logger.info(f"Agent result: {result}")
        return result


async def run_streamed(agent_request: Dict[str, Any]) -> EventSourceResponse:
    """
    异步运行 Agent
    """
    total_cost_time_start = time.time()

    input_items = agent_request.get("inputItems")
    logger.debug(f"################ origin input_items: {input_items}")
    variables = agent_request.get("variables", {})
    agent_txt = agent_request.get("agent", {})
    trigger = variables.get("trigger")

    agent_meta = AgentMeta.from_dict(agent_txt)

    async def sse_event_stream():
        # 执行stream时立即返回sse先与前端建立连接
        yield ServerSentEvent(data="")

        start_time = time.time()
        agent_exec_ctx = AgentExecutionContext(variables=variables)
        agent_result_text = ""  # 用于收集所有事件内容
        try:
            # 检查是否需要异步执行, skipEvaluation = true 表明跳过评估，允许用户可以跳过评估
            if not variables.get("skipEvaluation", False):
                # 评估任务是否需要异步执行
                evaluation_result = await TaskEvaluation.evaluate_task_complexity(input_items, agent_meta)
                if evaluation_result.result:
                    task_message = evaluation_result.get_task_start_message()
                    yield ServerSentEvent(data=task_message.model_dump_json())

                    feedback_message = evaluation_result.get_task_feedback_message()
                    yield ServerSentEvent(data=feedback_message.model_dump_json())

                    yield ServerSentEvent(data="[DONE]")
                    return  # 如果需要异步执行，直接返回，不继续执行后续代码

            if trigger is not None:
                # 如果以触发器的维度来执行则需要移除交接的Agent并且重新生成系统提示词
                reset_agent_from_trigger(agent_meta, trigger)
                starting_agent, agent_ctx = await create_agent(
                    agent_meta,
                    AgentContext(trigger=trigger, main_agent_lang_switch=agent_meta.props.reply_with_user_language)
                )
            else:
                starting_agent, agent_ctx = await get_agent_from_cache(agent_meta)
                # 如果有内置的mcp，则不能使用缓存
                if agent_ctx.has_any_built_in_mcp():
                    starting_agent, agent_ctx = await create_agent(
                        agent_meta,
                        AgentContext(main_agent_lang_switch=agent_meta.props.reply_with_user_language)
                    )

            agent_exec_ctx.from_agent_context(agent_ctx)
            # 设置对话历史
            agent_exec_ctx.set_conversation_history(input_items)
            # 记录 Agent 元数据
            agent_exec_ctx.agent_meta = agent_meta
            # 设置当前 Agent
            agent_exec_ctx.current_agent = starting_agent

            logger.info(f"################ Agent create {agent_meta.key},  cost: {round(time.time() - start_time, 2)}")

            list_input = await input_item_helpers.to_input_items(input_items)
            logger.debug(f"################ to_input_items: {list_input}")

            # 检查是否有缓存的工具调用
            matched_tool_call = match_tool_call_cache(input_items, agent_meta)
            if matched_tool_call:
                (
                    final_service_key,
                    final_params_str,
                    service_name,
                    service_key,
                    agent_name,
                ) = matched_tool_call

                logger.debug(f"################ got cached: {service_key}")

                # 直接调用服务
                result = await call_service(service_key=final_service_key, params=json.loads(final_params_str))

                # 生成所有消息
                cached_input_items, messages = create_cached_tool_call_messages(
                    agent_meta,
                    final_service_key,
                    final_params_str,
                    service_name,
                    result,
                )
                list_input.extend(cached_input_items)
                for message in messages:
                    yield ServerSentEvent(data=json.dumps(message))

                # 如果缓存中的工具，设置了最终输出结果，则返回，因为这里的缓存会导致智能体的工具行为失效
                tool_metadata = agent_exec_ctx.find_tool_in_agent(get_service_alias_key(service_key), agent_name)
                if tool_metadata is not None and tool_metadata.final_output:
                    # 内容输出
                    tool_result = result if isinstance(result, str) else json.dumps(result, ensure_ascii=False)
                    agent_message = AgentMessage(
                        content=AgentTextContent(text=tool_result, status="DONE"),
                        meta=AgentMessageMeta.from_agent_meta(agent_meta)
                    )
                    yield ServerSentEvent(data=agent_message.model_dump_json())
                    yield ServerSentEvent(data="[DONE]")
                    return

            logger.debug(f"################ list_input: {list_input}")

            start_time = time.time()
            async with MCPServerGuard(*agent_exec_ctx.mcp_servers):
                # 创建评估专家助手
                evaluator: EvaluationAgent = await EvaluationAgentFactory.create_evaluation_agent()
                # 评估的异步任务
                evaluation_task = None
                while True:
                    agent_result = Runner.run_streamed(
                        starting_agent=starting_agent,
                        input=list_input,
                        context=agent_exec_ctx,
                        max_turns=AgentConstants.DEFAULT_MAX_TURNS,
                    )

                    tool_call_count = 0
                    last_tool_call = None
                    last_agent = None

                    async for event in agent_result.stream_events():
                        # if event.type == "run_item_stream_event":
                        #     logger.info(f"[run_streamed] event: {event}")
                        # elif (event.type == "raw_response_event"
                        #       and not isinstance(event.data, ResponseTextDeltaEvent)
                        #       and not isinstance(event.data, ResponseFunctionCallArgumentsDeltaEvent)
                        #       and not isinstance(event.data, ResponseReasoningSummaryTextDeltaEvent)):
                        #     logger.info(f"[run_streamed] event: {event}")
                        # 更新当前Agent
                        agent_exec_ctx.current_agent = agent_result.current_agent
                        # agent_exec_ctx.set_conversation_history(agent_result.to_input_list())

                        if event.type == "run_item_stream_event":
                            if isinstance(event.item, HandoffOutputItem):
                                # agent_result_text += f"agent_handoff: {event.item.target_agent.name} from {event.item.source_agent.name}\n"
                                # # Agent 交接
                                # agent_message = AgentMessage.from_event(
                                #     event.item,
                                #     content_type="handoff",
                                #     context=agent_exec_ctx,
                                # )
                                # yield ServerSentEvent(data=agent_message.model_dump_json())
                                pass

                            elif isinstance(event.item, HandoffCallItem):
                                target_agent_key = event.item.raw_item.name
                                target_agent_meta = agent_exec_ctx.find_agent_meta(
                                    get_service_original_key(target_agent_key))
                                agent_result_text += f"agent_handoff: {target_agent_meta.name} from {agent_exec_ctx.current_agent.name}\n"
                                # Agent 交接
                                agent_message = AgentMessage.from_event(
                                    target_agent_meta,
                                    content_type="handoff",
                                    context=agent_exec_ctx,
                                )
                                yield ServerSentEvent(data=agent_message.model_dump_json())

                            elif isinstance(event.item, ToolCallOutputItem):
                                agent_result_text += (
                                    f"function_call_output: {obj_pretty(getattr(event.item, 'output', ''))}\n"
                                )
                                # 把手动添加来的工具也进行输出
                                tool_call_messages = agent_exec_ctx.create_tool_call_messages_with_manual(event.item)
                                for tool_call_message in tool_call_messages:
                                    yield ServerSentEvent(data=tool_call_message.model_dump_json())

                                # 工具调用参数透传
                                agent_message = agent_exec_ctx.create_tool_call_arguments_message(event.item)
                                if agent_message:
                                    arguments = agent_message.content.arguments
                                    if arguments:  # 只有当参数非空时才添加
                                        agent_result_text += f"function_call_arguments: {arguments or ''}\n"
                                    yield ServerSentEvent(data=agent_message.model_dump_json())

                                # 工具调用，返回输出
                                agent_messages = agent_exec_ctx.create_tool_output_message(event.item)
                                if agent_messages:
                                    for agent_message in agent_messages:
                                        yield ServerSentEvent(data=agent_message.model_dump_json())

                        elif event.type == "raw_response_event":
                            if (isinstance(event.data, ResponseOutputItemAddedEvent)
                                    and isinstance(event.data.item, ResponseFunctionToolCall)
                            ):
                                agent_result_text += f"function_call: {event.data.item.name}\n"

                                tool_call_count += 1
                                last_tool_call = event.data.item
                                last_agent = agent_exec_ctx.current_agent

                                # 工具调用，工具选择就绪
                                agent_message = agent_exec_ctx.create_tool_call_message(event.data.item)
                                if agent_message:
                                    yield ServerSentEvent(data=agent_message.model_dump_json())

                            elif (isinstance(event.data, ResponseOutputItemDoneEvent)
                                  and isinstance(event.data.item, ResponseFunctionToolCall)
                            ):
                                # 这里设置工具参数
                                agent_exec_ctx.update_tool_call_arguments(event.data.item.call_id,
                                                                          event.data.item.arguments)

                            elif isinstance(event.data, ResponseReasoningSummaryTextDeltaEvent):
                                # 推理流输出
                                agent_message = AgentMessage.from_event(event.data, "reasoning", context=agent_exec_ctx)
                                yield ServerSentEvent(data=agent_message.model_dump_json())

                            elif isinstance(event.data, ResponseReasoningSummaryPartDoneEvent):
                                # 推理流输出结束
                                agent_message = AgentMessage.from_event("", "reasoning", context=agent_exec_ctx)
                                yield ServerSentEvent(data=agent_message.model_dump_json())

                            elif isinstance(event.data, ResponseTextDeltaEvent):
                                # 文本流输出
                                if event.data.delta == "":
                                    continue  # 跳过空的文本流输出
                                agent_message = AgentMessage.from_event(event.data, "text", context=agent_exec_ctx)
                                agent_result_text += event.data.delta or ""
                                yield ServerSentEvent(data=agent_message.model_dump_json())

                            elif isinstance(event.data, ResponseContentPartDoneEvent):
                                agent_result_text += "\n"
                                # 文本流输出结束
                                agent_message = AgentMessage.from_event("", "text", context=agent_exec_ctx)
                                yield ServerSentEvent(data=agent_message.model_dump_json())

                            elif isinstance(event.data, ResponseCompletedEvent):
                                # 文本流输出环节全部完毕
                                agent_exec_ctx.update_token_usage(event.data)

                    # 异步结果评估
                    if (
                            not agent_exec_ctx.tools_to_final_output_result
                            and evaluator.enabled()
                    ):
                        starting_agent = agent_result.current_agent
                        result_list_input = agent_result.to_input_list()
                        list_input = input_item_helpers.to_input_items_remove_handoff_tools(result_list_input, agent_exec_ctx)
                        evaluation_task = asyncio.create_task(evaluator.run_agent(list_input))

                    # 如果只有一次工具调用且是系统服务，则缓存该调用
                    if tool_call_count == 1 and last_tool_call and last_agent:
                        tool_name = last_tool_call.name
                        call_id = last_tool_call.call_id
                        tool_call_mapping = agent_exec_ctx.tools_call_mapping.get(call_id)
                        if tool_call_mapping:
                            params_str = tool_call_mapping.arguments
                            invoke_status = tool_call_mapping.invoke_status
                            module_key = tool_call_mapping.module_key
                            current_agent_meta = agent_exec_ctx.get_agent_meta_by_name(
                                last_agent.name
                            )
                            cache_tool_call(
                                input_items,
                                agent_meta,
                                current_agent_meta,
                                get_service_original_key(tool_name),
                                params_str,
                                invoke_status,
                            )

                    # 输出最终结果，因为工具的使用行为改变后，llm就没有输出了，这里手动输出
                    if agent_exec_ctx.tools_to_final_output_result:
                        agent_message = AgentMessage.from_event(
                            event=agent_result.final_output
                            if isinstance(agent_result.final_output, str)
                            else str(agent_result.final_output),
                            content_type="text",
                            context=agent_exec_ctx
                        )
                        yield ServerSentEvent(data=agent_message.model_dump_json())

                    # 汇总统计token消耗量和总耗时
                    agent_exec_ctx.cost_time = round(time.time() - total_cost_time_start, 2)
                    cost_token_usage_message = agent_exec_ctx.create_token_usage_message()
                    agent_result_text += f"\ntokens: {cost_token_usage_message.content or ''}\n"
                    yield ServerSentEvent(data=cost_token_usage_message.model_dump_json())

                    # 等待评估结果并决策是否继续执行主流程（仅当已触发评估时）
                    if evaluation_task is not None:
                        try:
                            evaluator_result = await evaluation_task
                            if evaluator.meets_threshold(evaluator_result):
                                break
                            else:
                                # 评估不未通过，则继续执行一次
                                evaluation_task = None
                                agent_message = AgentMessage.from_event(
                                    event="检测到当前的结果不太符合您的意图，我将帮您重新执行，请稍等。\n",
                                    content_type="text",
                                    context=agent_exec_ctx
                                )
                                yield ServerSentEvent(data=agent_message.model_dump_json())
                                list_input.append(
                                    EasyInputMessageParam(
                                        content=f"评估反馈：{evaluator_result.feedback}，\n根据评估结果，您的回答未能满足用户需求，请根据反馈重新回答，确保能直接解决用户问题。",
                                        role="system",
                                        type="message"
                                    )
                                )
                                continue
                        except Exception as e:
                            logger.exception(f"Error awaiting evaluation result: {e}")
                            # 评估失败则直接结束，避免死循环
                            break
                    else:
                        break

                # 回调消息，告诉前端要进行回调操作
                agent_message = agent_exec_ctx.create_callback_message()
                if agent_message:
                    yield ServerSentEvent(data=agent_message.model_dump_json())
                # 结束当前请求
                yield ServerSentEvent(data="[DONE]")
        except Exception as e:
            logger.exception(f"Error executing stream event: {str(e)}")
            agent_result_text += f"error: {str(e) or ''}\n"
            agent_message = AgentMessage.from_event(e, content_type="error", context=agent_exec_ctx)
            yield ServerSentEvent(data=agent_message.model_dump_json())
            yield ServerSentEvent(data="[DONE]")
        finally:
            logger.debug(f"Agent result text:\n{agent_result_text}")
            logger.info(f"################total cost: {round(time.time() - start_time, 2)}")

    return EventSourceResponse(
        sse_event_stream(),
        media_type="text/event-stream",
        headers=SSE_STEAMING_HEADERS,
        sep="\n",
        ping_message_factory=lambda: ServerSentEvent(event="ping", data="", retry=None),
    )
