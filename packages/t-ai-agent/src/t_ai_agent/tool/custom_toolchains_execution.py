import json
import time
from typing import Any, Optional

import httpx
from agents import FunctionTool
from agents.tool_context import Tool<PERSON>ontext


from loguru import logger

from t_ai_common.utils.common import get_short_key
from .tool_call_execution import Tool<PERSON>allExecution, ToolCallExecutionModel
from ..model import AgentExecutionContext
from ..model.agent_dsl import AgentMeta


class CustomToolchainsInvokeParams(ToolCallExecutionModel):
    pass


class CustomToolchainsExecution(ToolCallExecution):
    async def build_tool(self, agent_meta: AgentMeta) -> FunctionTool:
        return FunctionTool(
            name=get_short_key(self.http_tool.key),
            description=self.http_tool.desc or self.http_tool.name,
            params_json_schema=await self.convert_to_params_json_schema(build_custom_toolchains_json_schema(self.http_tool)),
            on_invoke_tool=self.execute(agent_meta.name),
            strict_json_schema=True,
        )

    def execute(self, agent_name: str) -> Any:
        async def run_function(context: ToolContext, params_str=None) -> str:
            logger.debug(f"http_run_function invoke with params: {params_str}")

            too_call_id = context.tool_call_id
            agent_exec_ctx: AgentExecutionContext = context.context

            try:
                pass
            except Exception as e:
                import traceback

                stack_trace = traceback.format_exc()
                logger.exception(f"HTTP调用异常: {e}")
                logger.error(f"完整异常堆栈:\n{stack_trace}")

                err_result = f"Error executing HTTP tool: {str(e)}"

                # 更新工具调用状态为错误
                agent_exec_ctx.update_tool_call_status(too_call_id, "error", err_result)

                return err_result
        return run_function
