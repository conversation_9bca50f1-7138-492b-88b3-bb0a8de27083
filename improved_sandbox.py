"""
改进的沙箱实现 - 基于当前代码添加进程隔离
"""

import subprocess
import tempfile
import os
import json
import sys
import time
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class SandboxConfig:
    """沙箱配置"""
    timeout: int = 5
    memory_limit_mb: int = 100
    cpu_limit: float = 0.5
    allow_network: bool = False
    temp_dir: Optional[str] = None


class ImprovedPythonSandbox:
    """改进的Python沙箱 - 支持进程隔离"""
    
    def __init__(self, config: SandboxConfig = None):
        self.config = config or SandboxConfig()
        self.temp_dir = self.config.temp_dir or tempfile.gettempdir()
    
    def _create_restricted_runner_script(self) -> str:
        """创建受限的Python执行脚本"""
        return f'''
import sys
import resource
import signal
import os
import json
from io import StringIO
import contextlib

# 设置资源限制
def set_limits():
    try:
        # 内存限制
        memory_limit = {self.config.memory_limit_mb} * 1024 * 1024
        resource.setrlimit(resource.RLIMIT_AS, (memory_limit, memory_limit))
        
        # CPU时间限制
        resource.setrlimit(resource.RLIMIT_CPU, ({self.config.timeout}, {self.config.timeout}))
        
        # 禁止创建子进程
        resource.setrlimit(resource.RLIMIT_NPROC, (1, 1))
        
        # 禁止创建文件
        resource.setrlimit(resource.RLIMIT_FSIZE, (0, 0))
        
    except Exception as e:
        print(f"Warning: Could not set resource limits: {{e}}", file=sys.stderr)

# 超时处理
def timeout_handler(signum, frame):
    result = {{
        "success": False,
        "output": "",
        "error": "TIMEOUT: Code execution exceeded time limit",
        "execution_time": {self.config.timeout}
    }}
    print(json.dumps(result))
    sys.exit(1)

# 设置信号处理
signal.signal(signal.SIGALRM, timeout_handler)
signal.alarm({self.config.timeout})

# 设置资源限制
set_limits()

# 受限的内置函数
FORBIDDEN_BUILTINS = {{
    'open', 'eval', 'exec', 'compile', '__import__', 'input', 
    'raw_input', 'file', 'execfile', 'reload', 'vars', 'locals', 
    'globals', 'dir', 'memoryview', 'breakpoint'
}}

safe_builtins = {{}}
for name in dir(__builtins__):
    if name not in FORBIDDEN_BUILTINS:
        safe_builtins[name] = getattr(__builtins__, name)

# 安全的模块白名单
ALLOWED_MODULES = {{
    'math', 'random', 'time', 'datetime', 'collections', 
    'itertools', 'functools', 're', 'json', 'heapq', 'string'
}}

# 自定义导入函数
def safe_import(name, globals=None, locals=None, fromlist=(), level=0):
    root_module = name.split('.')[0]
    if root_module not in ALLOWED_MODULES:
        raise ImportError(f"Import of module '{{root_module}}' is not allowed")
    return __import__(name, globals, locals, fromlist, level)

# 设置安全环境
safe_globals = {{
    '__builtins__': safe_builtins,
    '__import__': safe_import,
}}

# 添加允许的模块
for module_name in ALLOWED_MODULES:
    try:
        safe_globals[module_name] = __import__(module_name)
    except ImportError:
        pass

# 执行用户代码
def execute_user_code():
    start_time = time.time()
    stdout = StringIO()
    stderr = StringIO()
    result = None
    
    try:
        # 读取用户代码
        user_code = sys.stdin.read()
        
        # 重定向输出
        with contextlib.redirect_stdout(stdout), contextlib.redirect_stderr(stderr):
            # 编译并执行代码
            compiled_code = compile(user_code, '<sandbox>', 'exec')
            local_vars = {{}}
            exec(compiled_code, safe_globals, local_vars)
            
            # 获取结果
            if 'result' in local_vars:
                result = local_vars['result']
        
        execution_time = time.time() - start_time
        
        # 返回结果
        output = {{
            "success": True,
            "result": str(result) if result is not None else None,
            "output": stdout.getvalue(),
            "error": stderr.getvalue(),
            "execution_time": execution_time
        }}
        
    except Exception as e:
        execution_time = time.time() - start_time
        output = {{
            "success": False,
            "result": None,
            "output": stdout.getvalue(),
            "error": f"{{type(e).__name__}}: {{str(e)}}\\n{{stderr.getvalue()}}",
            "execution_time": execution_time
        }}
    
    print(json.dumps(output))

if __name__ == "__main__":
    execute_user_code()
'''
    
    def run_code_isolated(self, code: str) -> Dict[str, Any]:
        """在隔离的子进程中执行代码"""
        
        # 创建运行脚本
        runner_script = self._create_restricted_runner_script()
        
        try:
            # 执行子进程
            process = subprocess.Popen(
                [sys.executable, '-c', runner_script],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=self.temp_dir,
                env={'PATH': ''}  # 清空环境变量
            )
            
            # 发送代码并等待结果
            stdout, stderr = process.communicate(
                input=code, 
                timeout=self.config.timeout + 2
            )
            
            # 解析结果
            try:
                result = json.loads(stdout)
                return result
            except json.JSONDecodeError:
                return {
                    "success": False,
                    "result": None,
                    "output": stdout,
                    "error": f"Failed to parse result. stderr: {stderr}",
                    "execution_time": 0
                }
                
        except subprocess.TimeoutExpired:
            try:
                process.kill()
                process.wait()
            except:
                pass
            return {
                "success": False,
                "result": None,
                "output": "",
                "error": "Process timeout - execution took too long",
                "execution_time": self.config.timeout
            }
        except Exception as e:
            return {
                "success": False,
                "result": None,
                "output": "",
                "error": f"Process execution error: {str(e)}",
                "execution_time": 0
            }


# 使用示例和测试
if __name__ == "__main__":
    # 配置沙箱
    config = SandboxConfig(
        timeout=3,
        memory_limit_mb=50,
        cpu_limit=0.3
    )
    
    sandbox = ImprovedPythonSandbox(config)
    
    # 测试正常代码
    test_code = '''
import math
print("Hello from isolated sandbox!")
print(f"Pi = {math.pi:.4f}")

# 计算斐波那契数列
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

result = fibonacci(10)
print(f"Fibonacci(10) = {result}")
'''
    
    print("=== 测试正常代码 ===")
    result = sandbox.run_code_isolated(test_code)
    print(f"Success: {result['success']}")
    print(f"Output: {result['output']}")
    print(f"Result: {result['result']}")
    print(f"Time: {result['execution_time']:.3f}s")
    
    # 测试恶意代码
    malicious_codes = [
        # 尝试导入禁止的模块
        "import os; os.system('ls')",
        
        # 尝试访问文件
        "open('/etc/passwd').read()",
        
        # 尝试使用eval
        "eval('print(\"hacked\")')",
        
        # 尝试无限循环
        "while True: pass",
        
        # 尝试大量内存分配
        "data = 'x' * (200 * 1024 * 1024)"
    ]
    
    print("\n=== 测试恶意代码 ===")
    for i, malicious_code in enumerate(malicious_codes, 1):
        print(f"\n--- 测试 {i}: {malicious_code[:50]}... ---")
        result = sandbox.run_code_isolated(malicious_code)
        print(f"Success: {result['success']}")
        print(f"Error: {result['error'][:100]}...")
        print(f"Time: {result['execution_time']:.3f}s")
