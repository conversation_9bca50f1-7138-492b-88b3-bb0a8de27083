"""
进程隔离的Python沙箱实现示例
展示如何通过子进程、容器等方式实现真正的隔离
"""

import subprocess
import tempfile
import os
import json
import signal
import time
from typing import Dict, Any, Optional
import multiprocessing
import sys


class ProcessIsolatedSandbox:
    """使用子进程实现的隔离沙箱"""
    
    def __init__(self, timeout: int = 5, memory_limit_mb: int = 100):
        self.timeout = timeout
        self.memory_limit_mb = memory_limit_mb
    
    def run_code_subprocess(self, code: str) -> Dict[str, Any]:
        """方案1: 使用subprocess创建子进程执行代码"""
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(code)
            temp_file = f.name
        
        try:
            # 构建受限的Python执行命令
            cmd = [
                sys.executable, '-c', f'''
import sys
import resource
import signal

# 设置资源限制
def set_limits():
    # 内存限制 (字节)
    memory_limit = {self.memory_limit_mb} * 1024 * 1024
    resource.setrlimit(resource.RLIMIT_AS, (memory_limit, memory_limit))
    
    # CPU时间限制
    resource.setrlimit(resource.RLIMIT_CPU, ({self.timeout}, {self.timeout}))
    
    # 禁止创建子进程
    resource.setrlimit(resource.RLIMIT_NPROC, (0, 0))

# 超时处理
def timeout_handler(signum, frame):
    print("TIMEOUT_ERROR: Code execution timed out")
    sys.exit(1)

signal.signal(signal.SIGALRM, timeout_handler)
signal.alarm({self.timeout})

try:
    set_limits()
    
    # 受限的内置函数
    restricted_builtins = {{
        name: getattr(__builtins__, name) 
        for name in dir(__builtins__) 
        if name not in ['open', 'eval', 'exec', 'compile', '__import__', 'input']
    }}
    
    # 执行用户代码
    with open("{temp_file}", "r") as f:
        user_code = f.read()
    
    exec(user_code, {{"__builtins__": restricted_builtins}})
    
except Exception as e:
    print(f"EXECUTION_ERROR: {{e}}")
    sys.exit(1)
'''
            ]
            
            # 执行子进程
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=self.timeout + 1,  # 稍微长一点的超时
                cwd=tempfile.gettempdir()  # 在临时目录执行
            )
            
            return {
                "success": result.returncode == 0,
                "output": result.stdout,
                "error": result.stderr,
                "return_code": result.returncode
            }
            
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "output": "",
                "error": "Process timeout",
                "return_code": -1
            }
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file)
            except:
                pass
    
    def run_code_multiprocessing(self, code: str) -> Dict[str, Any]:
        """方案2: 使用multiprocessing实现进程隔离"""
        
        def execute_in_process(code_str, result_queue):
            """在子进程中执行代码"""
            try:
                import resource
                import signal
                
                # 设置资源限制
                memory_limit = self.memory_limit_mb * 1024 * 1024
                resource.setrlimit(resource.RLIMIT_AS, (memory_limit, memory_limit))
                resource.setrlimit(resource.RLIMIT_CPU, (self.timeout, self.timeout))
                
                # 超时处理
                def timeout_handler(signum, frame):
                    result_queue.put({
                        "success": False,
                        "output": "",
                        "error": "Execution timeout",
                        "return_code": -1
                    })
                    os._exit(1)
                
                signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(self.timeout)
                
                # 重定向输出
                import io
                import contextlib
                
                stdout = io.StringIO()
                stderr = io.StringIO()
                
                # 受限环境
                restricted_globals = {
                    "__builtins__": {
                        name: getattr(__builtins__, name)
                        for name in dir(__builtins__)
                        if name not in ['open', 'eval', 'exec', 'compile', '__import__', 'input']
                    }
                }
                
                with contextlib.redirect_stdout(stdout), contextlib.redirect_stderr(stderr):
                    exec(code_str, restricted_globals)
                
                result_queue.put({
                    "success": True,
                    "output": stdout.getvalue(),
                    "error": stderr.getvalue(),
                    "return_code": 0
                })
                
            except Exception as e:
                result_queue.put({
                    "success": False,
                    "output": "",
                    "error": str(e),
                    "return_code": 1
                })
        
        # 创建进程和队列
        result_queue = multiprocessing.Queue()
        process = multiprocessing.Process(
            target=execute_in_process,
            args=(code, result_queue)
        )
        
        try:
            process.start()
            process.join(timeout=self.timeout + 1)
            
            if process.is_alive():
                process.terminate()
                process.join()
                return {
                    "success": False,
                    "output": "",
                    "error": "Process timeout",
                    "return_code": -1
                }
            
            if not result_queue.empty():
                return result_queue.get()
            else:
                return {
                    "success": False,
                    "output": "",
                    "error": "No result from process",
                    "return_code": -1
                }
                
        except Exception as e:
            if process.is_alive():
                process.terminate()
                process.join()
            return {
                "success": False,
                "output": "",
                "error": f"Process execution error: {e}",
                "return_code": -1
            }


class DockerIsolatedSandbox:
    """使用Docker容器实现的完全隔离沙箱"""
    
    def __init__(self, image: str = "python:3.9-alpine", timeout: int = 5):
        self.image = image
        self.timeout = timeout
    
    def run_code_docker(self, code: str) -> Dict[str, Any]:
        """方案3: 使用Docker容器执行代码"""
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(code)
            temp_file = f.name
        
        try:
            # Docker运行命令
            cmd = [
                'docker', 'run',
                '--rm',  # 自动删除容器
                '--network=none',  # 禁用网络
                '--memory=100m',  # 内存限制
                '--cpus=0.5',  # CPU限制
                '--read-only',  # 只读文件系统
                '--tmpfs=/tmp',  # 临时文件系统
                f'--volume={temp_file}:/code.py:ro',  # 挂载代码文件
                '--user=nobody',  # 非特权用户
                self.image,
                'python', '/code.py'
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=self.timeout
            )
            
            return {
                "success": result.returncode == 0,
                "output": result.stdout,
                "error": result.stderr,
                "return_code": result.returncode
            }
            
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "output": "",
                "error": "Docker container timeout",
                "return_code": -1
            }
        except FileNotFoundError:
            return {
                "success": False,
                "output": "",
                "error": "Docker not found. Please install Docker.",
                "return_code": -1
            }
        finally:
            try:
                os.unlink(temp_file)
            except:
                pass


# 使用示例
if __name__ == "__main__":
    # 测试代码
    test_code = """
import math
print("Hello from sandbox!")
print(f"Pi = {math.pi}")
result = 2 + 2
print(f"2 + 2 = {result}")
"""
    
    malicious_code = """
import os
os.system("ls /")  # 这应该被阻止
"""
    
    # 测试不同的隔离方案
    sandbox = ProcessIsolatedSandbox()
    
    print("=== 测试正常代码 ===")
    result1 = sandbox.run_code_subprocess(test_code)
    print(f"Subprocess结果: {result1}")
    
    result2 = sandbox.run_code_multiprocessing(test_code)
    print(f"Multiprocessing结果: {result2}")
    
    print("\n=== 测试恶意代码 ===")
    result3 = sandbox.run_code_subprocess(malicious_code)
    print(f"恶意代码结果: {result3}")
